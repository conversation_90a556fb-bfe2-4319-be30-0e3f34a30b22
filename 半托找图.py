import tkinter as tk
from tkinter import messagebox, scrolledtext
import requests
import os
import re
import json
import configparser
from datetime import datetime
from urllib.parse import quote
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
from PIL import Image, ImageTk
import io
import threading
import queue
import time
import pyperclip  # 添加剪贴板支持
import logging  # 添加日志模块


# 详细日志记录类
class DetailedLogger:
    """详细日志记录类，专门记录搜索和下载过程的详细信息"""

    def __init__(self, log_dir="运行日志"):
        """初始化日志记录器"""
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)

        # 创建日志文件名（按日期）
        timestamp = datetime.now().strftime("%Y%m%d")
        self.log_file = os.path.join(log_dir, f"详细运行日志_{timestamp}.log")

        # 配置日志记录器
        self.logger = logging.getLogger('DetailedLogger')
        self.logger.setLevel(logging.DEBUG)

        # 清除已有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 创建文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)

        # 添加处理器到日志记录器
        self.logger.addHandler(file_handler)

        # 记录日志开始
        self.logger.info("=" * 80)
        self.logger.info("详细运行日志开始记录")
        self.logger.info("=" * 80)

    def log_search_start(self, product_name, product_id, search_type="商品下载"):
        """记录搜索开始"""
        self.logger.info(f"[{search_type}] 开始处理商品")
        self.logger.info(f"  原始商品名称: {product_name}")
        self.logger.info(f"  商品ID/SKU: {product_id}")

    def log_name_processing(self, original_name, cleaned_name, processed_name, ignore_chars=False):
        """记录商品名称处理过程"""
        self.logger.info(f"[名称处理] 商品名称处理过程:")
        self.logger.info(f"  步骤1 - 原始名称: {original_name}")
        self.logger.info(f"  步骤2 - 清理后名称: {cleaned_name}")
        if ignore_chars:
            self.logger.info(f"  步骤3 - 启用忽略前后字符模式")
            self.logger.info(f"  步骤4 - 最终搜索词: {processed_name}")
        else:
            self.logger.info(f"  步骤3 - 最终搜索词: {processed_name}")

    def log_everything_search(self, search_query, search_params, search_url):
        """记录Everything搜索请求"""
        self.logger.info(f"[Everything搜索] 发起搜索请求:")
        self.logger.info(f"  搜索URL: {search_url}")
        self.logger.info(f"  搜索查询: {search_query}")
        self.logger.info(f"  搜索参数: {json.dumps(search_params, ensure_ascii=False, indent=2)}")

    def log_search_results(self, total_results, valid_files, filter_details=None):
        """记录搜索结果"""
        self.logger.info(f"[搜索结果] Everything返回结果:")
        self.logger.info(f"  总结果数: {total_results}")
        self.logger.info(f"  符合条件的文件数: {len(valid_files)}")

        if filter_details:
            self.logger.info(f"  过滤条件: {filter_details}")

        if valid_files:
            self.logger.info(f"  符合条件的文件列表:")
            for idx, (item, file_path) in enumerate(valid_files, 1):
                file_name = item.get('name', '')
                file_size = item.get('size', 0)
                self.logger.info(f"    {idx}. 文件名: {file_name}")
                self.logger.info(f"       完整路径: {file_path}")
                self.logger.info(f"       文件大小: {self._format_size(file_size)}")
        else:
            self.logger.info(f"  未找到符合条件的文件")

    def log_user_selection(self, selected_index, total_files, selection_type="本地文件"):
        """记录用户选择"""
        if selected_index is None:
            self.logger.info(f"[用户选择] 用户取消了选择")
        elif selected_index == -1:
            self.logger.info(f"[用户选择] 用户选择了API商品图")
        else:
            self.logger.info(f"[用户选择] 用户选择了第 {selected_index + 1} 张图片 (共 {total_files} 张)")
            self.logger.info(f"  选择类型: {selection_type}")

    def log_download_attempt(self, download_url, save_path, download_type="本地文件"):
        """记录下载尝试"""
        self.logger.info(f"[下载尝试] 开始下载 {download_type}:")
        self.logger.info(f"  下载URL: {download_url}")
        self.logger.info(f"  保存路径: {save_path}")

    def log_download_result(self, success, save_path, error_msg=None):
        """记录下载结果"""
        if success:
            self.logger.info(f"[下载结果] 下载成功")
            self.logger.info(f"  文件已保存到: {save_path}")
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path)
                self.logger.info(f"  文件大小: {self._format_size(file_size)}")
        else:
            self.logger.error(f"[下载结果] 下载失败")
            self.logger.error(f"  目标路径: {save_path}")
            if error_msg:
                self.logger.error(f"  错误信息: {error_msg}")

    def log_api_request(self, api_url, payload, response_status=None):
        """记录API请求"""
        self.logger.info(f"[API请求] 发起API请求:")
        self.logger.info(f"  请求URL: {api_url}")
        self.logger.info(f"  请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        if response_status:
            self.logger.info(f"  响应状态: {response_status}")

    def log_api_response(self, response_data, product_name=None, thumb_url=None):
        """记录API响应"""
        self.logger.info(f"[API响应] 收到API响应:")
        if product_name:
            self.logger.info(f"  获取到商品名称: {product_name}")
        if thumb_url:
            self.logger.info(f"  获取到商品图片URL: {thumb_url}")
        else:
            self.logger.info(f"  未获取到商品图片URL")

    def log_process_summary(self, product_name, product_id, success, final_result):
        """记录处理总结"""
        self.logger.info(f"[处理总结] 商品处理完成:")
        self.logger.info(f"  商品名称: {product_name}")
        self.logger.info(f"  商品ID/SKU: {product_id}")
        self.logger.info(f"  处理结果: {'成功' if success else '失败'}")
        self.logger.info(f"  详细结果: {final_result}")
        self.logger.info("-" * 60)

    def _format_size(self, bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 B"
        elif bytes_size < 1024:
            return f"{bytes_size} B"
        elif bytes_size < 1024 * 1024:
            return f"{bytes_size / 1024:.2f} KB"
        elif bytes_size < 1024 * 1024 * 1024:
            return f"{bytes_size / (1024 * 1024):.2f} MB"
        else:
            return f"{bytes_size / (1024 * 1024 * 1024):.2f} GB"


# 配置文件处理类
class ConfigManager:
    """配置文件管理类"""
    CONFIG_FILE = "settings.ini"
    
    @classmethod
    def load_config(cls):
        """加载配置文件"""
        config = configparser.ConfigParser(interpolation=None)  # 禁用插值功能
        
        # 默认配置
        default_settings = {
            'API': {
                'url': 'https://www.dianxiaomi.com/package/list.htm?pageNo=1&pageSize=300&shopId=6833815&state=paid&authId=-1&country=&platform=&isSearch=0&startTime=&endTime=&orderField=order_pay_time&isVoided=0&isRemoved=0&ruleId=-1&sysRule=&applyType=&applyStatus=&printJh=-1&printMd=-1&commitPlatform=&productStatus=&jhComment=-1&storageId=0&history=&custom=-1&isOversea=-1&timeOut=0&refundStatus=0&forbiddenStatus=-1&forbiddenReason=0&behindTrack=-1',
                'cookie': '',
                'base_url': 'https://www.dianxiaomi.com',
                'sku_search_url': 'https://www.dianxiaomi.com/api/popTemuProduct/pageList.json',
                'referer': 'https://www.dianxiaomi.com/'
            },
            'SEARCH': {
                'base_path': r"E:\图片\原图",
                'target_suffix': r"\导出图\已完成",
                'enable_target_suffix': 'True',
                'ignore_filename_chars': 'False',
                'ignore_prefix_count': '20',
                'ignore_suffix_count': '50'
            },
            'EVERYTHING': {
                'base_url': 'http://localhost:8080',
                'image_url': 'http://127.0.0.1:8080',
                'search_path': r"E:\图片\原图",
                'target_suffix': r"\导出图\已完成"
            },
            'SHARED': {
                'folder': r"\\*************\hhr-图库\合伙人-半托出单图\亚克力摆件\丽生-亚克力摆件"
            },
            'OPTIONS': {
                'strict_search': 'True'
            },
            'HEADERS': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'accept_encoding': 'gzip, deflate, br',
                'accept_language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'connection': 'keep-alive'
            }
        }
        
        # 检查配置文件是否存在
        if os.path.exists(cls.CONFIG_FILE):
            try:
                config.read(cls.CONFIG_FILE, encoding='utf-8')
            except Exception as e:
                print(f"读取配置文件错误: {e}")
        
        # 确保所有默认配置都存在
        for section, options in default_settings.items():
            if not config.has_section(section):
                config.add_section(section)
            for key, value in options.items():
                if not config.has_option(section, key):
                    config[section][key] = value
        
        # 保存更新后的配置
        cls.save_config(config)
        return config
    
    @classmethod
    def save_config(cls, config):
        """保存配置到文件"""
        try:
            with open(cls.CONFIG_FILE, 'w', encoding='utf-8') as f:
                config.write(f)
            return True
        except Exception as e:
            print(f"保存配置文件错误: {e}")
            return False
            
    @classmethod
    def update_config(cls, section, key, value):
        """更新单个配置项"""
        config = cls.load_config()
        if not config.has_section(section):
            config.add_section(section)
        config[section][key] = value
        return cls.save_config(config)

def validate_key():
    """验证 key.vdf 文件的有效性"""
    key_file = "key.vdf"
    if not os.path.exists(key_file):
        messagebox.showerror("错误", f"未找到授权文件 {key_file}")
        exit(1)

    # 密钥生成配置（必须与加密时一致）
    password = b'my_super_secret_password'
    salt = b'fixed_salt_value'
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 读取加密文件
    with open(key_file, "rb") as f:
        data = f.read()
        iv = data[:16]  # 前16字节为IV
        ciphertext = data[16:]  # 剩余部分为密文

    # 解密数据
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
    decryptor = cipher.decryptor()
    try:
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()
    except ValueError:
        messagebox.showerror("错误", "解密失败：数据可能被篡改")
        exit(1)

    # 移除填充
    unpadder = padding.PKCS7(128).unpadder()
    try:
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()
    except ValueError:
        messagebox.showerror("错误", "数据校验失败：填充错误")
        exit(1)

    # 验证时间有效性
    try:
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))
    except ValueError:
        messagebox.showerror("错误", "时间格式无效")
        exit(1)

    # 计算时间差
    time_difference = datetime.now() - stored_time

    # 有效期验证（30天）
    if time_difference > timedelta(days=30):
        messagebox.showerror("错误", "软件授权已过期")
        exit(1)
    else:
        print("验证通过，欢迎使用本软件")

class StyleConfig:
    """现代化界面样式配置"""
    FONT_NAME = "微软雅黑"
    validate_key()
    COLOR_SCHEME = {
        'primary_bg': '#F5F7FA',  # 主背景
        'secondary_bg': '#FFFFFF',  # 次背景
        'primary_text': '#2D3748',  # 主文字
        'secondary_text': '#4A5568',  # 次文字
        'accent_blue': '#4299E1',  # 主色调-蓝
        'accent_green': '#48BB78',  # 成功色-绿
        'accent_orange': '#ED8936',  # 警告色-橙
        'accent_gray': '#A0AEC0',  # 禁用色-灰
        'console_bg': '#FFFFFF',  # 控制台背景
        'border': '#E2E8F0'  # 边框色
    }

    FONT_SETTINGS = {
        'title': (FONT_NAME, 14, 'bold'),
        'subtitle': (FONT_NAME, 11),
        'normal': (FONT_NAME, 10),
        'button': (FONT_NAME, 10, 'bold'),
        'console': (FONT_NAME, 9),
        'status': (FONT_NAME, 9)
    }


# 图片选择对话框
class ImageSelectionDialog:
    """处理重复图片选择的对话框"""
    
    def __init__(self, parent, sku_or_product_id, image_files, callback, api_image_data=None):
        """
        初始化图片选择对话框
        
        Args:
            parent: 父窗口
            sku_or_product_id: SKU或产品ID
            image_files: 图片文件列表，格式为 [(item, file_path), ...]
            callback: 选择完成后的回调函数，接收选择的索引
            api_image_data: API商品主图的PIL.Image对象（如果有）
        """
        self.parent = parent
        self.sku_or_product_id = sku_or_product_id
        self.image_files = image_files
        self.callback = callback
        self.selected_index = None
        self.api_image_data = api_image_data
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"选择图片 - {sku_or_product_id}")
        self.dialog.geometry("1200x900")  # 增加窗口大小
        self.dialog.minsize(1200, 800)  # 增加最小窗口大小
        self.dialog.transient(parent)  # 设置为父窗口的临时窗口
        self.dialog.grab_set()  # 模态对话框
        
        self.setup_ui()
        self.load_images()
        
    def setup_ui(self):
        """设置UI界面"""
        # 主容器
        self.main_frame = tk.Frame(self.dialog, bg=StyleConfig.COLOR_SCHEME['primary_bg'])
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)  # 增加内边距
        
        # 标题标签
        self.title_label = tk.Label(
            self.main_frame,
            text=f"发现多张图片匹配 {self.sku_or_product_id}，请选择要保留的图片",
            font=StyleConfig.FONT_SETTINGS['subtitle'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.title_label.pack(fill=tk.X, pady=(0, 15))
        
        # 说明标签
        self.info_label = tk.Label(
            self.main_frame,
            text="右键点击要保留的图片，或使用下方按钮选择",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['accent_orange'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.info_label.pack(fill=tk.X, pady=(0, 15))
        
        # 创建水平分隔的主内容区域
        self.content_frame = tk.Frame(
            self.main_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧区域 - API商品主图预览
        if self.api_image_data:
            self.api_preview_frame = tk.Frame(
                self.content_frame,
                bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
                bd=1,
                relief=tk.GROOVE
            )
            self.api_preview_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10), pady=0)
            
            # API图片标题
            self.api_title_label = tk.Label(
                self.api_preview_frame,
                text="API商品主图（参考）",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['accent_blue'],
                fg='white',
                padx=10,
                pady=5
            )
            self.api_title_label.pack(fill=tk.X)
            
            # API图片预览区
            self.api_image_frame = tk.Frame(
                self.api_preview_frame,
                bg=StyleConfig.COLOR_SCHEME['secondary_bg']
            )
            self.api_image_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
            
            self.api_image_label = tk.Label(
                self.api_image_frame,
                bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
                text="加载中...",
                font=StyleConfig.FONT_SETTINGS['subtitle']
            )
            self.api_image_label.pack(fill=tk.BOTH, expand=True)
            
            # 尝试加载API图片
            try:
                # 直接使用内存中的图片数据
                if self.api_image_data:
                    api_image = self.create_thumbnail(self.api_image_data, (600, 550))  # 调整大小，进一步增大
                    self.api_image_label.config(image=api_image)
                    self.api_image_label.image = api_image  # 保持引用
            except Exception as e:
                self.api_image_label.config(text=f"无法加载API图片: {str(e)}")
        
        # 缩略图区域 - 使用Canvas和Scrollbar实现垂直滚动
        self.thumbnails_canvas_frame = tk.Frame(
            self.content_frame,
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            bd=1,
            relief=tk.GROOVE,
            width=200  # 增加宽度
        )
        self.thumbnails_canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, pady=0)
        self.thumbnails_canvas_frame.pack_propagate(False)  # 防止frame被内容撑开
        
        # 缩略图标题
        self.thumb_title_label = tk.Label(
            self.thumbnails_canvas_frame,
            text="本地缩略图",
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['accent_orange'],
            fg='white',
            padx=10,
            pady=5
        )
        self.thumb_title_label.pack(fill=tk.X)
        
        self.thumbnails_canvas = tk.Canvas(
            self.thumbnails_canvas_frame,
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            width=180  # 增加宽度
        )
        self.thumbnails_scrollbar = tk.Scrollbar(
            self.thumbnails_canvas_frame,
            orient=tk.VERTICAL,
            command=self.thumbnails_canvas.yview
        )
        
        self.thumbnails_frame = tk.Frame(
            self.thumbnails_canvas,
            bg=StyleConfig.COLOR_SCHEME['secondary_bg']
        )
        
        self.thumbnails_canvas.configure(yscrollcommand=self.thumbnails_scrollbar.set)
        
        # 布局滚动区域
        self.thumbnails_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.thumbnails_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        
        # 创建窗口
        self.thumbnails_canvas.create_window(
            (0, 0),
            window=self.thumbnails_frame,
            anchor=tk.NW,
            width=self.thumbnails_canvas.winfo_reqwidth()
        )
        
        # 创建浮动预览窗口（初始隐藏）
        self.popup_preview = None
        self.popup_window = None
        
        # 按钮区域
        self.button_frame = tk.Frame(
            self.main_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.button_frame.pack(fill=tk.X, pady=(15, 0))
        
        # API图片下载按钮
        if self.api_image_data:
            self.api_download_button = tk.Button(
                self.button_frame,
                text="使用API图片",
                font=StyleConfig.FONT_SETTINGS['button'],
                bg=StyleConfig.COLOR_SCHEME['accent_blue'],
                fg='white',
                relief=tk.FLAT,
                padx=20,
                pady=5,
                command=self.on_api_download
            )
            self.api_download_button.pack(side=tk.LEFT, padx=5)
        
        # 取消按钮
        self.cancel_button = tk.Button(
            self.button_frame,
            text="取消",
            font=StyleConfig.FONT_SETTINGS['button'],
            bg=StyleConfig.COLOR_SCHEME['accent_gray'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=5,
            command=self.on_cancel
        )
        self.cancel_button.pack(side=tk.RIGHT, padx=5)
        
        # 设置居中
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
        
        # 绑定滚动事件
        self.thumbnails_frame.bind("<Configure>", self.on_frame_configure)
        
    def on_api_download(self):
        """选择使用API图片"""
        self.selected_index = -1  # 使用-1表示选择API图片
        self.dialog.destroy()
        if self.callback:
            self.callback(-1)

    def on_frame_configure(self, event):
        """更新滚动区域"""
        self.thumbnails_canvas.configure(scrollregion=self.thumbnails_canvas.bbox("all"))

    def load_images(self):
        """加载图片"""
        self.image_queue = queue.Queue()
        self.thumbnail_refs = []  # 保持引用，防止垃圾回收
        
        # 启动线程加载图片
        threading.Thread(target=self._load_images_thread, daemon=True).start()
        
        # 设置定时器检查队列
        self.check_queue()
        
    def _load_images_thread(self):
        """在后台线程中加载图片"""
        # 从配置文件获取Everything图片URL
        config = ConfigManager.load_config()
        everything_image_url = config['EVERYTHING']['image_url']
        
        for idx, (item, file_path) in enumerate(self.image_files):
            try:
                # 使用Everything API获取图片
                image_url = f"{everything_image_url}/{quote(file_path)}"
                response = requests.get(image_url, timeout=10)
                response.raise_for_status()
                
                # 从响应内容创建图像
                img = Image.open(io.BytesIO(response.content))
                
                # 创建缩略图和悬停预览图
                thumbnail = self.create_thumbnail(img, (120, 120))  # 缩略图
                hover_image = self.create_thumbnail(img, (500, 400))  # 增大悬停预览大小
                
                # 将结果放入队列
                self.image_queue.put((idx, thumbnail, hover_image, file_path))
                
            except Exception as e:
                print(f"加载图片错误: {e}")
                # 加载失败时将错误信息放入队列
                self.image_queue.put((idx, None, None, None))
        
    def check_queue(self):
        """检查图片加载队列"""
        try:
            while not self.image_queue.empty():
                idx, thumbnail, hover_image, file_path = self.image_queue.get_nowait()
                
                if thumbnail:
                    # 创建缩略图容器
                    thumb_container = tk.Frame(
                        self.thumbnails_frame,
                        bg=StyleConfig.COLOR_SCHEME['secondary_bg']
                    )
                    thumb_container.pack(side=tk.TOP, pady=10, fill=tk.X)
                    
                    # 创建缩略图标签
                    thumb_label = tk.Label(
                        thumb_container,
                        image=thumbnail,
                        bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
                        bd=2,
                        relief=tk.FLAT
                    )
                    thumb_label.image = thumbnail  # 保持引用
                    thumb_label.pack(side=tk.TOP)
                    # 绑定右键点击事件
                    thumb_label.bind("<Button-3>", lambda e, i=idx: self.on_right_click(i))
                    
                    # 绑定鼠标悬停事件
                    thumb_label.bind("<Enter>", lambda e, img=hover_image, path=file_path: self.show_hover_preview(e, img, path))
                    thumb_label.bind("<Leave>", self.hide_hover_preview)
                    
                    # 创建选择按钮
                    select_btn = tk.Button(
                        thumb_container,
                        text=f"选择 {idx+1}",
                        font=StyleConfig.FONT_SETTINGS['normal'],
                        bg=StyleConfig.COLOR_SCHEME['accent_blue'],
                        fg='white',
                        relief=tk.FLAT,
                        command=lambda i=idx: self.on_select(i)
                    )
                    select_btn.pack(side=tk.TOP, pady=5)
                    
                    self.thumbnail_refs.append((thumb_label, select_btn, thumbnail, hover_image))
                    
            # 如果队列为空且没有加载任何图片，显示提示
            if not self.thumbnail_refs and not self.api_image_data:
                self.thumb_title_label.config(text="没有找到可用的图片")
                
        except Exception as e:
            print(f"处理图片队列错误: {e}")
            
        finally:
            # 每100毫秒检查一次队列
            self.dialog.after(100, self.check_queue)
            
    def create_thumbnail(self, img, size):
        """创建缩略图"""
        # 计算等比例缩放
        img_copy = img.copy()  # 创建副本
        img_copy.thumbnail(size, Image.LANCZOS)
        
        # 创建Tkinter可用的图像对象
        return ImageTk.PhotoImage(img_copy)
        
    def format_size(self, bytes_size):
        """格式化文件大小"""
        if bytes_size < 1024:
            return f"{bytes_size} B"
        elif bytes_size < 1024 * 1024:
            return f"{bytes_size / 1024:.2f} KB"
        elif bytes_size < 1024 * 1024 * 1024:
            return f"{bytes_size / (1024 * 1024):.2f} MB"
        else:
            return f"{bytes_size / (1024 * 1024 * 1024):.2f} GB"
            
    def on_thumbnail_click(self, idx):
        """点击缩略图时显示浮动预览"""
        try:
            # 获取所选图片的信息
            _, file_path = self.image_files[idx]
            
            # 显示悬停预览
            _, _, _, hover_image = self.thumbnail_refs[idx]
            if hover_image:
                self.show_hover_preview(None, hover_image, file_path)
                
        except Exception as e:
            print(f"显示预览错误: {e}")

    def on_right_click(self, idx):
        """右键点击选择图片"""
        self.on_select(idx)
        
    def on_select(self, idx):
        """选择图片"""
        self.selected_index = idx
        self.dialog.destroy()
        if self.callback:
            self.callback(idx)
            
    def on_cancel(self):
        """取消选择"""
        self.dialog.destroy()
        if self.callback:
            self.callback(None)
            
    def wait_for_selection(self):
        """等待用户选择"""
        self.dialog.wait_window()
        return self.selected_index

    def show_hover_preview(self, event, hover_image, file_path):
        """显示悬停预览窗口"""
        if self.popup_window:
            self.hide_hover_preview(None)
            
        # 创建一个顶层窗口
        self.popup_window = tk.Toplevel(self.dialog)
        self.popup_window.overrideredirect(True)  # 移除窗口边框
        self.popup_window.configure(bg=StyleConfig.COLOR_SCHEME['secondary_bg'])
        self.popup_window.wm_attributes("-topmost", 1)  # 置顶
        
        # 获取鼠标在屏幕上的位置
        x = self.dialog.winfo_pointerx() + 15
        y = self.dialog.winfo_pointery() - 200  # 向上偏移，避免遮挡缩略图
        
        # 确保预览窗口在屏幕内
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        
        if hover_image:
            img_width = hover_image.width()
            img_height = hover_image.height()
            
            # 调整位置，确保在屏幕内
            if x + img_width > screen_width:
                x = screen_width - img_width - 20
            if y + img_height > screen_height:
                y = screen_height - img_height - 20
            if y < 0:
                y = 0
        
        # 设置窗口位置
        self.popup_window.geometry(f"+{x}+{y}")
        
        # 创建图像标签
        if hover_image:
            self.popup_preview = tk.Label(
                self.popup_window,
                image=hover_image,
                bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
                bd=1,
                relief=tk.SOLID
            )
            self.popup_preview.image = hover_image  # 保持引用
            self.popup_preview.pack()

    def hide_hover_preview(self, event):
        """隐藏悬停预览窗口"""
        if self.popup_window:
            self.popup_window.destroy()
            self.popup_window = None
            self.popup_preview = None


class ImageDownloader:
    """图片下载功能模块"""
    # 添加可配置的路径检查条件
    target_path_suffix = r"\导出图\已完成"  # 默认值，将在程序启动时从配置文件更新
    enable_target_suffix = True  # 默认启用目标路径后缀
    ignore_filename_chars = False  # 是否忽略文件名前后字符
    ignore_prefix_count = 20  # 忽略文件名前面的字符数
    ignore_suffix_count = 50  # 忽略文件名后面的字符数

    @staticmethod
    def format_size(bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"
        
    @classmethod
    def process_search_term(cls, search_term):
        """处理搜索词，支持忽略前后字符"""
        # 如果不忽略前后字符，直接返回原始搜索词
        if not cls.ignore_filename_chars:
            return search_term
            
        # 如果搜索词长度小于忽略的字符数，直接返回原始搜索词
        if len(search_term) <= cls.ignore_prefix_count + cls.ignore_suffix_count:
            return search_term
            
        # 截取中间部分
        middle_part = search_term[cls.ignore_prefix_count:-cls.ignore_suffix_count if cls.ignore_suffix_count > 0 else None]
        return middle_part.strip()

    @classmethod
    def should_download_file(cls, file_path, file_name):
        """检查文件是否符合下载条件"""
        # 检查文件扩展名
        if not os.path.splitext(file_name)[1].lower() in ('.png', '.jpg', '.jpeg'):
            return False
            
        # 如果不启用目标路径后缀，则只检查文件扩展名
        if not cls.enable_target_suffix:
            return True
            
        # 启用目标路径后缀时，检查路径是否符合要求
        if cls.target_path_suffix:
            # 将文件路径转换为小写以进行不区分大小写的比较
            lower_file_path = file_path.lower()
            lower_suffix = cls.target_path_suffix.lower()
            
            # 检查文件路径是否包含目标路径后缀
            if not lower_file_path.endswith(lower_suffix + '\\' + file_name.lower()) and \
               not lower_file_path.endswith(lower_suffix + '/' + file_name.lower()):
                return False
                
        return True
        
    @classmethod
    def compare_filenames(cls, filename, search_term):
        """比较文件名和搜索词，支持忽略前后字符"""
        # 如果不忽略前后字符，直接比较文件名是否包含搜索词（不区分大小写）
        if not cls.ignore_filename_chars:
            return search_term.lower() in filename.lower()
            
        # 忽略前后字符的比较逻辑
        # 1. 获取文件名（不含扩展名）
        name_without_ext = os.path.splitext(filename)[0]
        
        # 2. 如果文件名长度小于忽略的字符数，直接进行模糊匹配
        if len(name_without_ext) <= cls.ignore_prefix_count + cls.ignore_suffix_count:
            # 对于太短的文件名，只要包含搜索词的一部分即可（取搜索词的前一半进行匹配）
            half_term_length = max(3, len(search_term) // 2)  # 至少取3个字符
            search_part = search_term[:half_term_length].lower()
            return search_part in name_without_ext.lower()
            
        # 3. 截取中间部分
        middle_part = name_without_ext[cls.ignore_prefix_count:-cls.ignore_suffix_count if cls.ignore_suffix_count > 0 else None]
        
        # 4. 模糊匹配：只要中间部分包含搜索词的一部分，或搜索词包含中间部分，就算匹配成功
        search_term_lower = search_term.lower()
        middle_part_lower = middle_part.lower()
        
        # 检查中间部分是否包含搜索词
        if search_term_lower in middle_part_lower:
            return True
            
        # 检查搜索词是否包含中间部分
        if middle_part_lower in search_term_lower:
            return True
            
        # 检查搜索词和中间部分是否有足够的相似性（至少有3个连续字符匹配）
        min_match_length = min(3, min(len(search_term_lower), len(middle_part_lower)))
        
        # 检查搜索词中是否有任何连续的部分匹配中间部分
        for i in range(len(search_term_lower) - min_match_length + 1):
            search_part = search_term_lower[i:i+min_match_length]
            if search_part in middle_part_lower:
                return True
                
        # 检查中间部分是否有任何连续的部分匹配搜索词
        for i in range(len(middle_part_lower) - min_match_length + 1):
            middle_part = middle_part_lower[i:i+min_match_length]
            if middle_part in search_term_lower:
                return True
                
        # 如果以上条件都不满足，则不匹配
        return False

    @classmethod
    def prepare_directory(cls, base_dir):
        """准备下载目录"""
        os.makedirs(base_dir, exist_ok=True)
        return base_dir


class SkuExtractor:
    """SKU提取功能模块"""
    
    @staticmethod
    def extract_skus(html_content):
        """从HTML内容中提取SKU编号"""
        # 正则表达式模式，用于匹配HTML中的SKU标识
        pattern = r'<a class="pairProInfoSku productUrl"[^>]*>([A-Za-z0-9]+)</a>'
        
        # 查找所有匹配
        matches = re.findall(pattern, html_content)
        
        # 返回去重后的结果
        return list(set(matches))
    
    @staticmethod
    def extract_product_name(html_content):
        """从HTML内容中提取商品名称"""
        # 正则表达式模式，用于匹配商品名称
        pattern = r'<span class="white-space no-new-line3 productHead">(.*?)</span>'
        
        # 查找匹配
        match = re.search(pattern, html_content)
        if match:
            return match.group(1).strip()
        return None
        
    @staticmethod
    def format_skus_for_display(skus):
        """将SKU列表格式化为显示文本"""
        if not skus:
            return "未找到SKU信息"
            
        return "\n".join([
            f"找到 {len(skus)} 个SKU:",
            "─" * 30,
            *[f"{idx+1}. {sku}" for idx, sku in enumerate(skus)],
            "─" * 30
        ])


class ModernProductApp:
    def __init__(self, root):
        self.root = root
        self.current_output_dir = ""
        # 添加默认配置
        self.config = ConfigManager.load_config()

        # 初始化详细日志记录器
        self.detailed_logger = DetailedLogger()
        
        # API相关配置
        self.search_base_path = self.config['SEARCH']['base_path']
        self.api_url = self.config['API']['url']
        self.api_cookie = self.config['API']['cookie']
        self.api_base_url = self.config['API']['base_url']
        self.api_sku_search_url = self.config['API']['sku_search_url']
        self.api_referer = self.config['API']['referer']
        
        # Everything服务配置
        self.everything_base_url = self.config['EVERYTHING']['base_url']
        self.everything_image_url = self.config['EVERYTHING']['image_url']
        
        # 共享文件夹配置
        self.shared_folder = self.config['SHARED']['folder']
        
        # 搜索选项配置
        self.strict_search_default = self.config['OPTIONS'].getboolean('strict_search', True)
        self.enable_target_suffix_default = self.config['SEARCH'].getboolean('enable_target_suffix', True)
        self.ignore_filename_chars_default = self.config['SEARCH'].getboolean('ignore_filename_chars', False)
        self.ignore_prefix_count_default = self.config['SEARCH'].getint('ignore_prefix_count', 20)
        self.ignore_suffix_count_default = self.config['SEARCH'].getint('ignore_suffix_count', 50)
        
        # HTTP请求头配置
        self.headers = {
            'User-Agent': self.config['HEADERS']['user_agent'],
            'Accept': self.config['HEADERS']['accept'],
            'Accept-Encoding': self.config['HEADERS']['accept_encoding'],
            'Accept-Language': self.config['HEADERS']['accept_language'],
            'Connection': self.config['HEADERS']['connection']
        }
        
        # 添加预览窗口引用
        self.preview_window = None
        
        # 添加变量跟踪修改状态，避免重复保存
        self.is_updating_config = False
        
        self.setup_window()
        self.create_widgets()
        self.setup_layout()
        # 设置默认值
        self.set_default_values()
        # 添加事件绑定
        self.bind_events()

        # 显示日志文件位置
        self.log(f"📝 详细运行日志将保存到: {self.detailed_logger.log_file}", 'info')

    def setup_window(self):
        """窗口基础配置"""
        self.root.title("商品数据提取工具")
        self.root.geometry("1000x850")
        self.root.minsize(800, 600)
        self.root.configure(bg=StyleConfig.COLOR_SCHEME['primary_bg'])

        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

    def create_widgets(self):
        """创建所有界面组件"""
        # 主容器
        self.main_container = tk.Frame(
            self.root,
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            padx=20,
            pady=15
        )

        # 标题区域
        self.title_frame = tk.Frame(
            self.main_container,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.title_label = tk.Label(
            self.title_frame,
            text="商品数据提取工具",
            font=StyleConfig.FONT_SETTINGS['title'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.subtitle_label = tk.Label(
            self.title_frame,
            text="提取商品信息并下载图片",
            font=StyleConfig.FONT_SETTINGS['subtitle'],
            fg=StyleConfig.COLOR_SCHEME['secondary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )

        # 配置区域
        self.config_frame = self.create_label_frame(" 配置选项 ")
        
        # 创建字符串变量，用于跟踪输入框内容变化
        self.api_url_var = tk.StringVar()
        self.cookie_var = tk.StringVar()
        self.search_path_var = tk.StringVar()
        self.path_filter_var = tk.StringVar()
        self.shared_folder_var = tk.StringVar()
        self.ignore_prefix_count_var = tk.StringVar(value=str(self.ignore_prefix_count_default))
        self.ignore_suffix_count_var = tk.StringVar(value=str(self.ignore_suffix_count_default))
        
        # API配置区域
        self.api_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.api_url_label = tk.Label(
            self.api_frame,
            text="API地址：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.api_url_entry = tk.Entry(
            self.api_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=50,
            textvariable=self.api_url_var
        )
        
        # Cookie配置
        self.cookie_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.cookie_label = tk.Label(
            self.cookie_frame,
            text="Cookie：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.cookie_entry = tk.Entry(
            self.cookie_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=50,
            textvariable=self.cookie_var
        )
        
        # 搜索路径配置
        self.path_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.search_path_label = tk.Label(
            self.path_frame,
            text="搜索路径：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.search_path_entry = tk.Entry(
            self.path_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=50,
            textvariable=self.search_path_var
        )
        self.browse_path_btn = tk.Button(
            self.path_frame, 
            text="浏览",
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['accent_blue'],
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=self.browse_search_path
        )
        
        # 路径后缀限制配置
        self.filter_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.path_filter_label = tk.Label(
            self.filter_frame,
            text="目标路径后缀：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.path_filter_entry = tk.Entry(
            self.filter_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=50,
            textvariable=self.path_filter_var
        )
        
        # 添加启用目标路径后缀的复选框
        self.enable_target_suffix_var = tk.BooleanVar(value=True)
        self.enable_target_suffix_cb = tk.Checkbutton(
            self.filter_frame,
            text="启用",
            variable=self.enable_target_suffix_var,
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            selectcolor=StyleConfig.COLOR_SCHEME['secondary_bg'],
            command=self.save_enable_target_suffix_option
        )
        
        # 添加忽略文件名前后字符的配置框架
        self.ignore_chars_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        
        # 添加忽略文件名前后字符的复选框
        self.ignore_filename_chars_var = tk.BooleanVar(value=self.ignore_filename_chars_default)
        self.ignore_filename_chars_cb = tk.Checkbutton(
            self.ignore_chars_frame,
            text="忽略文件名前后字符",
            variable=self.ignore_filename_chars_var,
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            selectcolor=StyleConfig.COLOR_SCHEME['secondary_bg'],
            command=self.save_ignore_filename_chars_option
        )
        
        # 忽略前缀字符数输入框
        self.ignore_prefix_label = tk.Label(
            self.ignore_chars_frame,
            text="忽略前",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.ignore_prefix_entry = tk.Entry(
            self.ignore_chars_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=3,
            textvariable=self.ignore_prefix_count_var
        )
        self.ignore_prefix_unit = tk.Label(
            self.ignore_chars_frame,
            text="个字符",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        
        # 忽略后缀字符数输入框
        self.ignore_suffix_label = tk.Label(
            self.ignore_chars_frame,
            text="忽略后",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.ignore_suffix_entry = tk.Entry(
            self.ignore_chars_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=3,
            textvariable=self.ignore_suffix_count_var
        )
        self.ignore_suffix_unit = tk.Label(
            self.ignore_chars_frame,
            text="个字符",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        
        # 共享文件夹配置
        self.shared_folder_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.shared_folder_label = tk.Label(
            self.shared_folder_frame,
            text="共享文件夹：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.shared_folder_entry = tk.Entry(
            self.shared_folder_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=50,
            textvariable=self.shared_folder_var
        )
        self.browse_shared_btn = tk.Button(
            self.shared_folder_frame, 
            text="浏览",
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['accent_blue'],
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=self.browse_shared_folder
        )
        
        # 其他搜索选项
        self.options_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        
        self.strict_search_var = tk.BooleanVar(value=True)
        self.strict_search_cb = tk.Checkbutton(
            self.options_frame,
            text="严格搜索路径限制（只搜索指定目录）",
            variable=self.strict_search_var,
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            selectcolor=StyleConfig.COLOR_SCHEME['secondary_bg'],
            command=self.save_strict_search_option
        )
        
        self.help_text = tk.Label(
            self.options_frame,
            text="注意：勾选\"严格搜索路径限制\"可确保只在指定目录中搜索",
            font=(StyleConfig.FONT_NAME, 9, "italic"),
            fg=StyleConfig.COLOR_SCHEME['accent_orange'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )

        # 商品数据输入区域
        self.product_frame = self.create_label_frame(" 商品数据输入 ")
        self.product_data_area = scrolledtext.ScrolledText(
            self.product_frame,
            wrap=tk.WORD,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            padx=15,
            pady=15,
            height=3,
            insertbackground=StyleConfig.COLOR_SCHEME['primary_text'],
            selectbackground=StyleConfig.COLOR_SCHEME['accent_blue']
        )
        
        # 添加SKU搜索商品名称功能
        self.sku_search_frame = tk.Frame(
            self.product_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            pady=10
        )
        self.sku_search_label = tk.Label(
            self.sku_search_frame,
            text="SKU搜索：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=10,
            anchor='e'
        )
        self.sku_search_entry = tk.Entry(
            self.sku_search_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=30
        )
        self.sku_search_btn = tk.Button(
            self.sku_search_frame,
            text="搜索商品名",
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg="#9F7AEA",  # 紫色按钮
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=self.search_sku_name
        )

        # 按钮区域
        self.btn_frame = tk.Frame(
            self.main_container,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.download_btn = self.create_button("下载图片", StyleConfig.COLOR_SCHEME['accent_orange'],
                                               self.download_product_images)
        self.rename_btn = self.create_button("重命名文件", StyleConfig.COLOR_SCHEME['accent_blue'],
                                            self.rename_files)
        self.extract_sku_btn = self.create_button("提取SKU", StyleConfig.COLOR_SCHEME['accent_green'],
                                                self.extract_sku)
        self.search_sku_btn = self.create_button("SKU搜索下载", "#9F7AEA",  # 紫色按钮
                                               self.search_sku_and_download)
        self.compare_sku_btn = self.create_button("SKU对比", "#F56565",  # 红色按钮
                                               self.compare_sku)
        self.download_missing_btn = self.create_button("下载缺失SKU", "#48BB78",  # 绿色按钮
                                               self.download_missing_sku)

        # 控制台输出
        self.console_frame = self.create_label_frame(" 操作日志 ")
        self.console = scrolledtext.ScrolledText(
            self.console_frame,
            wrap=tk.WORD,
            font=StyleConfig.FONT_SETTINGS['console'],
            bg=StyleConfig.COLOR_SCHEME['console_bg'],
            fg=StyleConfig.COLOR_SCHEME['secondary_text'],
            state='disabled',
            padx=15,
            pady=15,
            height=25
        )

        # 状态栏
        self.status_bar = tk.Label(
            self.root,
            text="就绪",
            font=StyleConfig.FONT_SETTINGS['status'],
            fg=StyleConfig.COLOR_SCHEME['secondary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            bd=1,
            relief=tk.SUNKEN,
            anchor=tk.W
        )

    def setup_layout(self):
        """组件布局"""
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self.title_frame.pack(fill=tk.X, pady=(0, 10))
        self.title_label.pack(side=tk.TOP, anchor='w')
        self.subtitle_label.pack(side=tk.TOP, anchor='w')
        
        # 配置区域
        self.config_frame.pack(fill=tk.X, pady=(0, 15))
        
        # API配置
        self.api_frame.pack(fill=tk.X, pady=(5, 5))
        self.api_url_label.pack(side=tk.LEFT)
        self.api_url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Cookie配置
        self.cookie_frame.pack(fill=tk.X, pady=(0, 5))
        self.cookie_label.pack(side=tk.LEFT)
        self.cookie_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 搜索路径配置
        self.path_frame.pack(fill=tk.X, pady=(5, 5))
        self.search_path_label.pack(side=tk.LEFT)
        self.search_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.browse_path_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # 路径后缀限制配置
        self.filter_frame.pack(fill=tk.X, pady=(0, 5))
        self.path_filter_label.pack(side=tk.LEFT)
        self.path_filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.enable_target_suffix_cb.pack(side=tk.LEFT, padx=(5, 0))
        
        # 忽略文件名前后字符配置
        self.ignore_chars_frame.pack(fill=tk.X, pady=(0, 5))
        self.ignore_filename_chars_cb.pack(side=tk.LEFT, padx=(15, 10))
        self.ignore_prefix_label.pack(side=tk.LEFT, padx=(0, 5))
        self.ignore_prefix_entry.pack(side=tk.LEFT)
        self.ignore_prefix_unit.pack(side=tk.LEFT, padx=(0, 10))
        self.ignore_suffix_label.pack(side=tk.LEFT, padx=(0, 5))
        self.ignore_suffix_entry.pack(side=tk.LEFT)
        self.ignore_suffix_unit.pack(side=tk.LEFT)
        
        # 共享文件夹配置
        self.shared_folder_frame.pack(fill=tk.X, pady=(5, 5))
        self.shared_folder_label.pack(side=tk.LEFT)
        self.shared_folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.browse_shared_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # 其他搜索选项
        self.options_frame.pack(fill=tk.X, pady=(0, 5))
        self.strict_search_cb.pack(side=tk.LEFT, padx=(15, 0))
        self.help_text.pack(side=tk.LEFT, padx=(10, 0))

        # 商品数据输入区域
        self.product_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        self.product_data_area.pack(fill=tk.BOTH, expand=True)
        
        # SKU搜索商品名称区域
        self.sku_search_frame.pack(fill=tk.X, pady=(5, 0))
        self.sku_search_label.pack(side=tk.LEFT, padx=(15, 0))
        self.sku_search_entry.pack(side=tk.LEFT, padx=(0, 5))
        self.sku_search_btn.pack(side=tk.LEFT)

        # 按钮区域
        self.btn_frame.pack(fill=tk.X, pady=(0, 15))
        self.download_btn.pack(side=tk.LEFT, padx=5)
        self.rename_btn.pack(side=tk.LEFT, padx=5)
        self.extract_sku_btn.pack(side=tk.LEFT, padx=5)
        self.search_sku_btn.pack(side=tk.LEFT, padx=5)
        self.compare_sku_btn.pack(side=tk.LEFT, padx=5)
        self.download_missing_btn.pack(side=tk.LEFT, padx=5)

        # 控制台输出
        self.console_frame.pack(fill=tk.BOTH, expand=True)
        self.console.pack(fill=tk.BOTH, expand=True)

        # 状态栏
        self.status_bar.pack(fill=tk.X, padx=20, pady=(0, 10))
        
    def set_default_values(self):
        """设置默认值"""
        # 使用变量设置，这样不会触发trace回调
        self.is_updating_config = True
        self.api_url_var.set(self.api_url)
        self.cookie_var.set(self.api_cookie)
        self.search_path_var.set(self.search_base_path)
        self.path_filter_var.set(self.config['SEARCH']['target_suffix'])
        self.shared_folder_var.set(self.shared_folder)
        self.strict_search_var.set(self.strict_search_default)
        self.enable_target_suffix_var.set(self.enable_target_suffix_default)
        self.ignore_filename_chars_var.set(self.ignore_filename_chars_default)
        self.ignore_prefix_count_var.set(str(self.ignore_prefix_count_default))
        self.ignore_suffix_count_var.set(str(self.ignore_suffix_count_default))
        self.is_updating_config = False
        
    def bind_events(self):
        """绑定事件处理函数"""
        # 添加变量跟踪
        self.api_url_var.trace_add("write", self.on_api_url_changed)
        self.cookie_var.trace_add("write", self.on_cookie_changed)
        self.search_path_var.trace_add("write", self.on_search_path_changed)
        self.path_filter_var.trace_add("write", self.on_path_filter_changed)
        self.shared_folder_var.trace_add("write", self.on_shared_folder_changed)
        self.ignore_prefix_count_var.trace_add("write", self.on_ignore_prefix_count_changed)
        self.ignore_suffix_count_var.trace_add("write", self.on_ignore_suffix_count_changed)
        
    # 添加变量变化的回调函数
    def on_api_url_changed(self, *args):
        """API URL变化时的回调函数"""
        if not self.is_updating_config:
            api_url = self.api_url_var.get().strip()
            ConfigManager.update_config('API', 'url', api_url)
            self.api_url = api_url
            self.log("已保存API URL配置", 'info')
            
    def on_cookie_changed(self, *args):
        """Cookie变化时的回调函数"""
        if not self.is_updating_config:
            cookie = self.cookie_var.get().strip()
            ConfigManager.update_config('API', 'cookie', cookie)
            self.api_cookie = cookie
            self.log("已保存Cookie配置", 'info')
            
    def on_search_path_changed(self, *args):
        """搜索路径变化时的回调函数"""
        if not self.is_updating_config:
            search_path = self.search_path_var.get().strip()
            ConfigManager.update_config('SEARCH', 'base_path', search_path)
            self.search_base_path = search_path
            self.log("已保存搜索路径配置", 'info')
            
    def on_path_filter_changed(self, *args):
        """路径过滤器变化时的回调函数"""
        if not self.is_updating_config:
            path_filter = self.path_filter_var.get().strip()
            ConfigManager.update_config('SEARCH', 'target_suffix', path_filter)
            # 更新下载器配置
            if path_filter:
                if path_filter.startswith('\\'):
                    ImageDownloader.target_path_suffix = path_filter
                else:
                    ImageDownloader.target_path_suffix = '\\' + path_filter
            self.log("已保存目标路径后缀配置", 'info')
            
    def on_shared_folder_changed(self, *args):
        """共享文件夹变化时的回调函数"""
        if not self.is_updating_config:
            shared_folder = self.shared_folder_var.get().strip()
            # 添加共享文件夹配置区
            if not self.config.has_section('SHARED'):
                self.config.add_section('SHARED')
            ConfigManager.update_config('SHARED', 'folder', shared_folder)
            self.log("已保存共享文件夹配置", 'info')
            
    def save_strict_search_option(self):
        """保存严格搜索选项"""
        if not self.is_updating_config:
            strict_search = self.strict_search_var.get()
            # 添加搜索选项配置区
            if not self.config.has_section('OPTIONS'):
                self.config.add_section('OPTIONS')
            ConfigManager.update_config('OPTIONS', 'strict_search', str(strict_search))
            self.log("已保存严格搜索选项配置", 'info')

    def save_enable_target_suffix_option(self):
        """保存启用目标路径后缀选项"""
        if not self.is_updating_config:
            enable_target_suffix = self.enable_target_suffix_var.get()
            ConfigManager.update_config('SEARCH', 'enable_target_suffix', str(enable_target_suffix))
            self.log("已保存启用目标路径后缀选项配置", 'info')

    def save_ignore_filename_chars_option(self):
        """保存忽略文件名前后字符选项"""
        if not self.is_updating_config:
            ignore_filename_chars = self.ignore_filename_chars_var.get()
            ConfigManager.update_config('SEARCH', 'ignore_filename_chars', str(ignore_filename_chars))
            self.log("已保存忽略文件名前后字符选项配置", 'info')
            
    def on_ignore_prefix_count_changed(self, *args):
        """忽略前缀字符数变化时的回调函数"""
        if not self.is_updating_config:
            try:
                prefix_count = int(self.ignore_prefix_count_var.get().strip())
                if prefix_count < 0:
                    prefix_count = 0
                    self.ignore_prefix_count_var.set(str(prefix_count))
                ConfigManager.update_config('SEARCH', 'ignore_prefix_count', str(prefix_count))
                self.log("已保存忽略前缀字符数配置", 'info')
            except ValueError:
                # 如果输入不是有效的整数，恢复为默认值
                self.ignore_prefix_count_var.set(str(self.ignore_prefix_count_default))
                
    def on_ignore_suffix_count_changed(self, *args):
        """忽略后缀字符数变化时的回调函数"""
        if not self.is_updating_config:
            try:
                suffix_count = int(self.ignore_suffix_count_var.get().strip())
                if suffix_count < 0:
                    suffix_count = 0
                    self.ignore_suffix_count_var.set(str(suffix_count))
                ConfigManager.update_config('SEARCH', 'ignore_suffix_count', str(suffix_count))
                self.log("已保存忽略后缀字符数配置", 'info')
            except ValueError:
                # 如果输入不是有效的整数，恢复为默认值
                self.ignore_suffix_count_var.set(str(self.ignore_suffix_count_default))

    def browse_search_path(self):
        """浏览并选择搜索路径"""
        folder_path = filedialog.askdirectory(
            title="选择搜索路径",
            initialdir=self.search_path_entry.get() or os.path.dirname(os.path.abspath(__file__))
        )
        if folder_path:
            # 通过变量设置，会自动触发trace回调
            self.search_path_var.set(folder_path)

    def browse_shared_folder(self):
        """浏览并选择共享文件夹"""
        folder_path = filedialog.askdirectory(
            title="选择共享文件夹",
            initialdir=self.shared_folder_entry.get() or os.path.dirname(os.path.abspath(__file__))
        )
        if folder_path:
            # 通过变量设置，会自动触发trace回调
            self.shared_folder_var.set(folder_path)

    def create_label_frame(self, text):
        """创建统一风格的标签框架"""
        return tk.LabelFrame(
            self.main_container,
            text=text,
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            padx=10,
            pady=10,
            relief=tk.FLAT,
            bd=1,
            highlightbackground=StyleConfig.COLOR_SCHEME['border']
        )

    def create_button(self, text, bg_color, command):
        """创建统一风格的按钮"""
        return tk.Button(
            self.btn_frame,
            text=text,
            font=StyleConfig.FONT_SETTINGS['button'],
            bg=bg_color,
            fg='#FFFFFF',
            activebackground=bg_color,
            activeforeground='#FFFFFF',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=command
        )

    def log(self, message, message_type='info'):
        """控制台日志输出"""
        color_map = {
            'info': StyleConfig.COLOR_SCHEME['secondary_text'],
            'success': StyleConfig.COLOR_SCHEME['accent_green'],
            'warning': StyleConfig.COLOR_SCHEME['accent_orange'],
            'error': '#E53E3E'
        }

        self.console.config(state='normal')
        self.console.insert(tk.END, message + "\n", message_type)
        self.console.tag_config(message_type, foreground=color_map.get(message_type, 'black'))
        self.console.see(tk.END)
        self.console.config(state='disabled')
        self.root.update()

    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
        self.root.update()

    def get_product_data(self):
        """获取商品数据"""
        data_text = self.product_data_area.get("1.0", tk.END).strip()
        if not data_text:
            messagebox.showwarning("警告", "请输入商品数据")
            return []
            
        products = []
        for line in data_text.split('\n'):
            line = line.strip()
            if line:
                # 检查是否有分隔符"----"
                if '----' in line:
                    name, product_id = line.split('----', 1)
                    products.append((name, product_id))
                else:
                    # 没有分隔符时，直接将整行作为SKU/ID
                    products.append((line, line))
                
        return products

    def download_product_images(self):
        """下载商品图片"""
        products = self.get_product_data()
        if not products:
            messagebox.showwarning("警告", "没有有效的商品数据")
            return
            
        try:
            # 更新配置（从用户输入获取）
            search_path = self.search_path_var.get().strip()
            path_filter = self.path_filter_var.get().strip()
            strict_search = self.strict_search_var.get()
            enable_target_suffix = self.enable_target_suffix_var.get()
            ignore_filename_chars = self.ignore_filename_chars_var.get()
            
            # 更新下载器配置
            ImageDownloader.enable_target_suffix = enable_target_suffix
            ImageDownloader.ignore_filename_chars = ignore_filename_chars
            
            # 更新忽略字符数配置
            try:
                prefix_count = int(self.ignore_prefix_count_var.get().strip())
                if prefix_count >= 0:
                    ImageDownloader.ignore_prefix_count = prefix_count
            except ValueError:
                pass
                
            try:
                suffix_count = int(self.ignore_suffix_count_var.get().strip())
                if suffix_count >= 0:
                    ImageDownloader.ignore_suffix_count = suffix_count
            except ValueError:
                pass
            
            if path_filter:
                # 确保路径过滤器格式正确（不管用户是否输入了反斜杠）
                if path_filter.startswith('\\'):
                    ImageDownloader.target_path_suffix = path_filter
                else:
                    ImageDownloader.target_path_suffix = '\\' + path_filter
            
            # 简化配置信息
            self.log(f"📝 当前配置: 搜索路径={search_path}, 目标路径={ImageDownloader.target_path_suffix}, 启用目标路径后缀={enable_target_suffix}", 'info')
            if ignore_filename_chars:
                self.log(f"📝 忽略文件名前{ImageDownloader.ignore_prefix_count}个字符和后{ImageDownloader.ignore_suffix_count}个字符", 'info')
                self.log(f"📝 已启用模糊匹配模式，将更宽松地匹配文件名", 'info')
            
            # 创建时间戳和输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"商品数据_{timestamp}_共{len(products)}组"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name
            self.log(f"创建输出文件夹: {folder_name}", 'info')
            
            # 创建商品列表文件
            txt_path = os.path.join(folder_name, "商品列表.txt")
            with open(txt_path, 'w', encoding='utf-8') as f:
                for name, product_id in products:
                    f.write(f"{name}----{product_id}\n")

            self.log("\n" + "═" * 60 + "\n开始下载商品图片...", 'info')
            self.update_status("正在下载商品图片...")

            base_dir = os.path.join(self.current_output_dir, "出单图下载")
            ImageDownloader.prepare_directory(base_dir)

            for name, pid in products:
                result = self.process_product(name, pid, base_dir, search_path)
                self.log(result, 'info')

            self.log("═" * 60 + "\n图片下载完成！", 'success')
            self.update_status("图片下载完成")
            messagebox.showinfo("完成", "商品图片下载完成！")

        except Exception as e:
            messagebox.showerror("错误", f"下载失败: {str(e)}")
            self.log(f"× 下载失败: {str(e)}", 'error')

    def process_product(self, product_name, product_id, base_dir, search_path=""):
        """处理单个商品"""
        try:
            # 记录搜索开始
            self.detailed_logger.log_search_start(product_name, product_id, "商品下载")

            # 移除硬编码的路径前缀
            # 只保留商品名称作为搜索条件
            cleaned_name = product_name.replace("2D Flat ", "").strip()

            # 处理搜索词，支持忽略前后字符
            processed_name = ImageDownloader.process_search_term(cleaned_name)

            # 记录名称处理过程
            self.detailed_logger.log_name_processing(
                product_name, cleaned_name, processed_name,
                ImageDownloader.ignore_filename_chars
            )

            # 构建限定路径的搜索查询
            search_query = processed_name

            # 如果指定了搜索路径并启用了严格路径搜索
            if search_path and self.strict_search_var.get():
                if not search_path.endswith('\\'):
                    search_path += '\\'
                # 使用Everything的path:语法限制搜索范围
                search_query = f"path:\"{search_path}\" {search_query}"

            # 简化日志输出
            self.log(f"🔍 搜索: {product_name}", 'info')
            if ImageDownloader.ignore_filename_chars:
                self.log(f"🔍 原始搜索词: {cleaned_name}", 'info')
                self.log(f"🔍 处理后的搜索词: {processed_name}", 'info')
                self.log(f"🔍 使用模糊匹配模式搜索，忽略前{ImageDownloader.ignore_prefix_count}个和后{ImageDownloader.ignore_suffix_count}个字符", 'info')

            # EverythingSearch参数格式化
            search_params = {
                "search": search_query,
                "json": 1,
                "path_column": 1,
                "size_column": 1,
                "sort": "name",
                "ascending": 1
            }

            # 从配置文件获取Everything服务地址
            search_url = self.everything_base_url

            # 记录Everything搜索请求
            self.detailed_logger.log_everything_search(search_query, search_params, search_url)

            # 简化日志输出
            self.log(f"🔍 搜索: {product_name}", 'info')
            if ImageDownloader.ignore_filename_chars:
                self.log(f"🔍 使用模糊匹配模式搜索，将忽略文件名前{ImageDownloader.ignore_prefix_count}个和后{ImageDownloader.ignore_suffix_count}个字符", 'info')

            try:
                response = requests.get(
                    search_url,
                    params=search_params,
                    timeout=30
                )
                response.raise_for_status()  # 检查HTTP错误
                data = response.json()

                # 移除API返回数据的详细日志

            except requests.RequestException as e:
                self.log(f"❌ API请求失败: {str(e)}", 'error')
                self.detailed_logger.log_process_summary(product_name, product_id, False, f"API请求错误 - {str(e)}")
                return f"处理商品『{product_name}』失败: API请求错误 - {str(e)}"
            except json.JSONDecodeError as e:
                self.log(f"❌ JSON解析失败: {str(e)}", 'error')
                self.log(f"原始响应内容: {response.text[:200]}...", 'error')
                self.detailed_logger.log_process_summary(product_name, product_id, False, f"JSON解析错误 - {str(e)}")
                return f"处理商品『{product_name}』失败: JSON解析错误 - {str(e)}"
            
            # 添加调试信息
            total_results = len(data.get('results', []))
            self.log(f"📊 搜索结果: 找到 {total_results} 个匹配项", 'info')

            if total_results == 0:
                self.log("⚠️ 未找到任何结果", 'warning')
                self.detailed_logger.log_search_results(total_results, [])
                self.detailed_logger.log_process_summary(product_name, product_id, False, "未找到任何匹配项")
                return f"『{product_name}』未找到任何匹配项"

            valid_files = []
            filter_details = {
                "启用目标路径后缀": ImageDownloader.enable_target_suffix,
                "目标路径后缀": ImageDownloader.target_path_suffix if ImageDownloader.enable_target_suffix else "无",
                "忽略文件名前后字符": ImageDownloader.ignore_filename_chars,
                "忽略前缀字符数": ImageDownloader.ignore_prefix_count if ImageDownloader.ignore_filename_chars else 0,
                "忽略后缀字符数": ImageDownloader.ignore_suffix_count if ImageDownloader.ignore_filename_chars else 0
            }

            for item in data.get("results", []):
                file_name = item.get('name', '')
                file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")

                # 检查文件是否符合下载条件
                should_download = ImageDownloader.should_download_file(file_path, file_name)

                # 使用compare_filenames方法检查文件名是否匹配搜索词
                name_matches = ImageDownloader.compare_filenames(file_name, cleaned_name)

                if should_download and name_matches:
                    valid_files.append((item, file_path))

            # 记录搜索结果
            self.detailed_logger.log_search_results(total_results, valid_files, filter_details)

            if not valid_files:
                self.detailed_logger.log_process_summary(product_name, product_id, False, "未找到符合条件文件")
                return f"『{product_name}』未找到符合条件文件"

            # 准备下载目录
            target_dir = ImageDownloader.prepare_directory(base_dir)
            total_files = len(valid_files)

            # 无论找到几张图片，都显示选择对话框
            self.log(f"📊 找到 {total_files} 张匹配图片，打开预览窗口", 'info')

            # 创建并显示图片选择对话框
            selection_dialog = ImageSelectionDialog(self.root, product_id, valid_files, None)
            selected_idx = selection_dialog.wait_for_selection()

            # 记录用户选择
            self.detailed_logger.log_user_selection(selected_idx, total_files, "本地文件")

            if selected_idx is not None:
                # 用户选择了图片
                item, file_path = valid_files[selected_idx]
                original_ext = os.path.splitext(item['name'])[1].lower()
                new_name = f"{product_id}{original_ext}"
                local_path = os.path.join(target_dir, new_name)
                image_url = f"{self.everything_image_url}/{quote(file_path)}"

                # 记录下载尝试
                self.detailed_logger.log_download_attempt(image_url, local_path, "本地文件")

                success = self.download_image(image_url, local_path)

                # 记录下载结果
                self.detailed_logger.log_download_result(success, local_path)

                status = f"√ 下载成功 → {new_name}" if success else "× 下载失败"

                result = [
                    f"\n🔍 商品名称: {product_name}",
                    f"🆔 商品ID: {product_id}",
                    f"📁 从 {total_files} 张图片中选择了第 {selected_idx+1} 张",
                    f"状态: {status}",
                    f"📊 最终结果: {'成功' if success else '失败'}下载 1 个文件",
                    f"📂 保存路径: {os.path.abspath(target_dir)}",
                    "═" * 60
                ]

                # 记录处理总结
                self.detailed_logger.log_process_summary(product_name, product_id, success, status)

                return "\n".join(result)
            else:
                # 用户取消了选择
                self.log(f"⚠️ 用户取消了图片选择", 'warning')
                self.detailed_logger.log_process_summary(product_name, product_id, False, "用户取消了图片选择")
                return f"『{product_name}』用户取消了图片选择"

        except Exception as e:
            self.detailed_logger.log_process_summary(product_name, product_id, False, f"处理出错: {str(e)}")
            return f"处理商品『{product_name}』出错: {str(e)}"

    def download_image(self, url, save_path, headers=None):
        """下载图片文件"""
        if headers is None:
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Referer': self.api_referer,
                'Accept': self.headers['Accept'],
                'Accept-Encoding': self.headers['Accept-Encoding'],
                'Accept-Language': self.headers['Accept-Language'],
                'Connection': self.headers['Connection']
            }

        try:
            response = requests.get(url, headers=headers, stream=True, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)
                return True
            else:
                error_msg = f"HTTP状态码: {response.status_code}"
                self.log(f"下载失败: {error_msg}", 'error')
                return False
        except Exception as e:
            error_msg = str(e)
            self.log(f"下载异常: {error_msg}", 'error')
            return False

    def rename_files(self):
        """重命名文件功能"""
        try:
            # 获取当前运行目录作为默认目录
            default_dir = os.path.dirname(os.path.abspath(__file__))
            
            folder_path = filedialog.askdirectory(
                title="请选择要处理的文件夹",
                initialdir=default_dir
            )
            if not folder_path:
                self.log("未选择文件夹，操作取消", 'warning')
                return

            self.log("\n" + "=" * 50 + "\n开始重命名文件...", 'info')
            self.update_status("正在重命名文件...")

            success_count = 0
            skip_count = 0
            error_count = 0

            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)

                # 跳过子目录
                if not os.path.isfile(file_path):
                    continue

                # 分离文件名和扩展名
                name_part, ext = os.path.splitext(filename)

                # 查找最后的下划线位置
                last_underscore = name_part.rfind('_')

                # 构建新文件名
                if last_underscore != -1:
                    new_name = name_part[:last_underscore] + ext
                    new_path = os.path.join(folder_path, new_name)

                    # 执行重命名（添加防冲突判断）
                    try:
                        if not os.path.exists(new_path):
                            os.rename(file_path, new_path)
                            self.log(f"成功: {filename} → {new_name}", 'success')
                            success_count += 1
                        else:
                            self.log(f"跳过: {new_name} 已存在（避免覆盖）", 'warning')
                            skip_count += 1
                    except Exception as e:
                        self.log(f"错误: 重命名 {filename} 失败 - {str(e)}", 'error')
                        error_count += 1
                else:
                    self.log(f"跳过: {filename} 无下划线", 'info')
                    skip_count += 1

            # 生成报告
            report = [
                "\n" + "=" * 50,
                "重命名完成！",
                f"处理文件夹: {folder_path}",
                f"成功重命名: {success_count}",
                f"跳过文件: {skip_count}",
                f"失败: {error_count}"
            ]

            self.log("\n".join(report), 'info')
            self.update_status("重命名完成")
            messagebox.showinfo("完成", "\n".join(report[1:]))

        except Exception as e:
            messagebox.showerror("错误", f"重命名过程出错: {str(e)}")
            self.log(f"× 重命名过程出错: {str(e)}", 'error')

    def extract_sku(self):
        """从API提取SKU信息"""
        try:
            # 获取当前配置
            api_url = self.api_url_var.get().strip()
            cookie = self.cookie_var.get().strip()
            
            # 保存到配置文件 - 不需要手动保存，因为变量trace已经处理了
            # ConfigManager.update_config('API', 'url', api_url)
            # ConfigManager.update_config('API', 'cookie', cookie)
            # self.api_url = api_url
            # self.api_cookie = cookie
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            self.log("\n" + "═" * 60 + "\n开始从API提取SKU编号...", 'info')
            self.update_status("正在请求数据...")
            
            # 设置请求头
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer
            }
            
            # 发送请求
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 提取SKU
            self.log("数据获取成功，正在提取SKU...", 'info')
            skus = SkuExtractor.extract_skus(response.text)
            
            # 格式化并显示结果
            result_text = SkuExtractor.format_skus_for_display(skus)
            self.log(result_text, 'success')
            
            # 结果写入输入框 - 简化为只显示SKU值
            if skus:
                self.product_data_area.delete("1.0", tk.END)
                for sku in skus:
                    # 修改为每行只添加SKU，不再使用"----"分隔符
                    self.product_data_area.insert(tk.END, f"{sku}\n")
                self.log(f"已将 {len(skus)} 个SKU自动填入数据输入区", 'success')
            
            self.update_status("SKU提取完成")
            
        except requests.RequestException as e:
            self.log(f"请求失败: {str(e)}", 'error')
            messagebox.showerror("错误", f"API请求失败: {str(e)}")
        except Exception as e:
            self.log(f"SKU提取过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"SKU提取过程出错: {str(e)}")

    def search_sku_and_download(self):
        """SKU搜索并下载图片"""
        try:
            # 获取输入的SKU列表
            skus = []
            for line in self.product_data_area.get("1.0", tk.END).strip().split('\n'):
                sku = line.strip()
                if sku:
                    skus.append(sku)
            
            if not skus:
                messagebox.showwarning("警告", "请输入SKU数据")
                return
                
            # 获取当前配置
            cookie = self.cookie_var.get().strip()
            search_path = self.search_path_var.get().strip()
            path_filter = self.path_filter_var.get().strip()
            enable_target_suffix = self.enable_target_suffix_var.get()
            ignore_filename_chars = self.ignore_filename_chars_var.get()
            
            # 保存更新的配置 - 不需要手动保存，因为变量trace已经处理了
            # ConfigManager.update_config('API', 'cookie', cookie)
            # ConfigManager.update_config('SEARCH', 'base_path', search_path)
            # ConfigManager.update_config('SEARCH', 'target_suffix', path_filter)
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            # 更新下载器路径配置
            ImageDownloader.enable_target_suffix = enable_target_suffix
            ImageDownloader.ignore_filename_chars = ignore_filename_chars
            
            # 更新忽略字符数配置
            try:
                prefix_count = int(self.ignore_prefix_count_var.get().strip())
                if prefix_count >= 0:
                    ImageDownloader.ignore_prefix_count = prefix_count
            except ValueError:
                pass
                
            try:
                suffix_count = int(self.ignore_suffix_count_var.get().strip())
                if suffix_count >= 0:
                    ImageDownloader.ignore_suffix_count = suffix_count
            except ValueError:
                pass
                
            if path_filter:
                if path_filter.startswith('\\'):
                    ImageDownloader.target_path_suffix = path_filter
                else:
                    ImageDownloader.target_path_suffix = '\\' + path_filter
                    
            # 创建时间戳和输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"SKU搜索_{timestamp}_共{len(skus)}个"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name
            
            # 创建请求会话
            session = requests.Session()
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 设置正确的请求地址和参数
            api_url = self.api_sku_search_url
            
            self.log("\n" + "═" * 60 + "\n开始SKU搜索并下载图片...", 'info')
            self.update_status("正在处理SKU...")
            
            # 输出配置信息
            self.log(f"📝 当前配置: 搜索路径={search_path}, 目标路径={ImageDownloader.target_path_suffix}, 启用目标路径后缀={enable_target_suffix}", 'info')
            if ignore_filename_chars:
                self.log(f"📝 忽略文件名前{ImageDownloader.ignore_prefix_count}个字符和后{ImageDownloader.ignore_suffix_count}个字符", 'info')
                self.log(f"📝 已启用模糊匹配模式，将更宽松地匹配文件名", 'info')
            
            # 准备下载目录
            base_dir = os.path.join(self.current_output_dir, "SKU图片下载")
            ImageDownloader.prepare_directory(base_dir)
            
            # 保存SKU列表
            sku_list_path = os.path.join(folder_name, "SKU列表.txt")
            with open(sku_list_path, 'w', encoding='utf-8') as f:
                for sku in skus:
                    f.write(f"{sku}\n")
            
            # 处理每个SKU
            success_count = 0
            for idx, sku in enumerate(skus, 1):
                try:
                    self.update_status(f"处理中: SKU {sku} ({idx}/{len(skus)})")
                    self.log(f"\n[{idx}/{len(skus)}] 🔍 正在查询SKU: {sku}", 'info')
                    
                    # 设置请求参数 - 使用正确的参数格式
                    payload = {
                        'sortName': '2',
                        'pageNo': '1',
                        'pageSize': '50',
                        'searchType': '2',
                        'searchValue': sku,
                        'productSearchType': '1',
                        'shopId': '-1',
                        'dxmState': 'online',
                        'site': '0',
                        'fullCid': '',
                        'sortValue': '2',
                        'productType': '',
                        'productStatus': 'ONLINE'
                    }
                    
                    # 记录API请求
                    self.detailed_logger.log_api_request(api_url, payload)

                    # 发送请求获取商品信息
                    response = session.post(api_url, headers=headers, data=payload, timeout=30)
                    response.raise_for_status()

                    # 解析JSON响应
                    json_data = response.json()

                    # 记录API响应状态
                    self.detailed_logger.log_api_request(api_url, payload, f"HTTP {response.status_code}")

                    # 检查是否成功
                    if json_data.get('code') != 0 or 'data' not in json_data:
                        error_msg = json_data.get('msg', '未知错误')
                        self.log(f"⚠️ 查询SKU {sku} 返回错误: {error_msg}", 'warning')
                        self.detailed_logger.log_api_response(json_data)
                        continue

                    # 检查是否有商品数据
                    product_list = json_data.get('data', {}).get('page', {}).get('list', [])
                    if not product_list:
                        self.log(f"⚠️ 未找到SKU {sku} 的商品信息", 'warning')
                        self.detailed_logger.log_api_response(json_data)
                        continue

                    # 获取第一个商品信息
                    product_info = product_list[0]
                    product_name = product_info.get('productName', '')

                    if not product_name:
                        self.log(f"⚠️ 未找到SKU {sku} 的商品名称", 'warning')
                        self.detailed_logger.log_api_response(json_data)
                        continue

                    self.log(f"📝 找到商品: {product_name}", 'info')

                    # 获取variations信息和thumbUrl
                    thumb_url = None
                    variations = product_info.get('variations', [])
                    if variations:
                        thumb_url = variations[0].get('thumbUrl')
                        if thumb_url:
                            self.log(f"🖼️ 获取到商品图片URL: {thumb_url}", 'info')
                        else:
                            self.log(f"⚠️ 未找到SKU {sku} 的thumbUrl", 'warning')
                    else:
                        self.log(f"⚠️ 未找到SKU {sku} 的variations信息", 'warning')

                    # 记录API响应
                    self.detailed_logger.log_api_response(json_data, product_name, thumb_url)
                    
                    # 使用商品名称搜索图片，并传入thumbUrl用于对比
                    result = self.process_sku_product(sku, product_name, base_dir, search_path, thumb_url)
                    if "成功下载" in result:
                        success_count += 1
                    self.log(result, 'info')
                    
                except Exception as e:
                    self.log(f"❌ 处理SKU {sku} 时出错: {str(e)}", 'error')
            
            # 汇总结果
            summary = f"\n═" * 30 + f"\n总计处理: {len(skus)} 个SKU, 成功下载: {success_count} 个\n文件保存在: {os.path.abspath(base_dir)}"
            self.log(summary, 'success')
            self.update_status("SKU下载完成")
            messagebox.showinfo("完成", f"缺失SKU图片下载完成！\n成功: {success_count}/{len(skus)}")
            
        except Exception as e:
            self.log(f"SKU下载过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"SKU下载过程出错: {str(e)}")
    
    def compare_sku(self):
        """SKU对比功能"""
        try:
            # 获取当前配置
            cookie = self.cookie_var.get().strip()
            shared_folder = self.shared_folder_var.get().strip()
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            if not shared_folder:
                messagebox.showwarning("警告", "请输入共享文件夹路径")
                return
                
            self.log("\n" + "═" * 60 + "\n开始SKU对比...", 'info')
            self.update_status("正在处理SKU对比...")
            
            # 设置请求头
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer
            }
            
            # 发送请求获取SKU列表
            api_url = self.api_url_var.get().strip()
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 提取SKU
            self.log("数据获取成功，正在提取SKU...", 'info')
            api_skus = SkuExtractor.extract_skus(response.text)
            
            if not api_skus:
                self.log("未从API获取到SKU数据", 'warning')
                return
                
            # 检查共享文件夹中的文件
            if not os.path.exists(shared_folder):
                self.log(f"无法访问共享文件夹: {shared_folder}", 'error')
                messagebox.showerror("错误", "无法访问共享文件夹")
                return
                
            # 获取共享文件夹中的文件名（SKU）
            shared_skus = set()
            for filename in os.listdir(shared_folder):
                # 获取文件名（不含扩展名）作为SKU
                sku = os.path.splitext(filename)[0]
                shared_skus.add(sku)
            
            # 对比SKU
            missing_skus = set(api_skus) - shared_skus
            
            # 显示结果
            self.log(f"\n对比结果:", 'info')
            self.log(f"API中的SKU总数: {len(api_skus)}", 'info')
            self.log(f"共享文件夹中的SKU总数: {len(shared_skus)}", 'info')
            self.log(f"缺失的SKU数量: {len(missing_skus)}", 'info')
            
            # 显示缺失的SKU列表
            if missing_skus:
                self.log("\n缺失的SKU列表:", 'info')
                self.log("─" * 30, 'info')
                for idx, sku in enumerate(sorted(missing_skus), 1):
                    self.log(f"{idx}. {sku}", 'info')
                self.log("─" * 30, 'info')
            
            # 将缺失的SKU显示在输入框中
            self.product_data_area.delete("1.0", tk.END)
            for sku in sorted(missing_skus):
                self.product_data_area.insert(tk.END, f"{sku}\n")
            
            self.update_status("SKU对比完成")
            messagebox.showinfo("完成", f"SKU对比完成！\n缺失SKU数量: {len(missing_skus)}")
            
        except requests.RequestException as e:
            self.log(f"请求失败: {str(e)}", 'error')
            messagebox.showerror("错误", f"API请求失败: {str(e)}")
        except Exception as e:
            self.log(f"SKU对比过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"SKU对比过程出错: {str(e)}")

    def download_api_image(self, thumb_url, api_image_path, max_retries=3, retry_interval=2):
        """下载API商品图片，支持重试机制
        
        Args:
            thumb_url: API图片URL
            api_image_path: 本地保存路径
            max_retries: 最大重试次数
            retry_interval: 重试间隔（秒）
            
        Returns:
            bool: 是否下载成功
        """
        api_headers = {
            'User-Agent': self.headers['User-Agent'],
            'Referer': self.api_referer,
            'Accept': self.headers['Accept'],
            'Accept-Encoding': self.headers['Accept-Encoding'],
            'Accept-Language': self.headers['Accept-Language'],
            'Connection': self.headers['Connection']
        }
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.log(f"🔄 第 {attempt + 1} 次重试下载API商品图...", 'info')
                    time.sleep(retry_interval)
                
                success = self.download_image(thumb_url, api_image_path, api_headers)
                if success:
                    self.log(f"✅ API商品图下载成功: {api_image_path}", 'success')
                    return True
                else:
                    self.log(f"❌ 第 {attempt + 1} 次下载API商品图失败", 'error')
                    
            except Exception as e:
                self.log(f"❌ 第 {attempt + 1} 次下载API商品图出错: {str(e)}", 'error')
                
        return False

    def process_sku_product(self, sku, product_name, base_dir, search_path="", thumb_url=None):
        """处理单个SKU商品的图片搜索和下载"""
        try:
            # 记录搜索开始
            self.detailed_logger.log_search_start(product_name, sku, "SKU搜索下载")

            # 处理搜索词，支持忽略前后字符
            processed_name = ImageDownloader.process_search_term(product_name)

            # 记录名称处理过程
            self.detailed_logger.log_name_processing(
                product_name, product_name, processed_name,
                ImageDownloader.ignore_filename_chars
            )

            # 构建限定路径的搜索查询
            search_query = processed_name
            
            # 如果指定了搜索路径并启用了严格路径搜索
            if search_path and self.strict_search_var.get():
                if not search_path.endswith('\\'):
                    search_path += '\\'
                # 使用Everything的path:语法限制搜索范围
                search_query = f"path:\"{search_path}\" {search_query}"

            # EverythingSearch参数格式化
            search_params = {
                "search": search_query,
                "json": 1,
                "path_column": 1,
                "size_column": 1,
                "sort": "name",
                "ascending": 1
            }

            # 从配置文件获取Everything服务地址
            search_url = self.everything_base_url

            # 记录Everything搜索请求
            self.detailed_logger.log_everything_search(search_query, search_params, search_url)

            # 输出搜索信息
            self.log(f"🔍 搜索SKU: {sku}, 商品名: {product_name}", 'info')
            if ImageDownloader.ignore_filename_chars:
                self.log(f"🔍 原始搜索词: {product_name}", 'info')
                self.log(f"🔍 处理后的搜索词: {processed_name}", 'info')
                self.log(f"🔍 使用模糊匹配模式搜索，忽略前{ImageDownloader.ignore_prefix_count}个和后{ImageDownloader.ignore_suffix_count}个字符", 'info')

            try:
                response = requests.get(
                    search_url,
                    params=search_params,
                    timeout=30
                )
                response.raise_for_status()
                data = response.json()

            except requests.RequestException as e:
                self.detailed_logger.log_process_summary(product_name, sku, False, f"API请求错误 - {str(e)}")
                return f"处理SKU {sku} 失败: API请求错误 - {str(e)}"
            except json.JSONDecodeError as e:
                self.detailed_logger.log_process_summary(product_name, sku, False, f"JSON解析错误 - {str(e)}")
                return f"处理SKU {sku} 失败: JSON解析错误 - {str(e)}"
            
            # 统计结果
            total_results = len(data.get('results', []))

            valid_files = []
            filter_details = {
                "启用目标路径后缀": ImageDownloader.enable_target_suffix,
                "目标路径后缀": ImageDownloader.target_path_suffix if ImageDownloader.enable_target_suffix else "无",
                "忽略文件名前后字符": ImageDownloader.ignore_filename_chars,
                "忽略前缀字符数": ImageDownloader.ignore_prefix_count if ImageDownloader.ignore_filename_chars else 0,
                "忽略后缀字符数": ImageDownloader.ignore_suffix_count if ImageDownloader.ignore_filename_chars else 0
            }

            if total_results > 0:
                for item in data.get("results", []):
                    file_name = item.get('name', '')
                    file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")

                    # 检查文件是否符合下载条件
                    should_download = ImageDownloader.should_download_file(file_path, file_name)

                    # 使用compare_filenames方法检查文件名是否匹配搜索词
                    name_matches = ImageDownloader.compare_filenames(file_name, product_name)

                    if should_download and name_matches:
                        valid_files.append((item, file_path))

            # 记录搜索结果
            self.detailed_logger.log_search_results(total_results, valid_files, filter_details)

            if total_results == 0 or not valid_files:
                # 如果本地没有找到图片，但有API图片，直接下载API图片
                if thumb_url:
                    self.log(f"🔍 本地未找到{'任何' if total_results == 0 else '符合条件的'}图片，但有API商品图，直接下载API图片", 'info')
                    local_path = os.path.join(base_dir, f"{sku}.jpg")

                    # 记录下载尝试
                    self.detailed_logger.log_download_attempt(thumb_url, local_path, "API商品图")

                    success = self.download_api_image(thumb_url, local_path)

                    # 记录下载结果
                    self.detailed_logger.log_download_result(success, local_path)

                    status = "从API直接下载图片"

                    result = [
                        f"SKU: {sku}",
                        f"商品: {product_name}",
                        f"本地未找到{'任何' if total_results == 0 else '符合条件的'}图片",
                        f"状态: {status}",
                        f"成功下载: {1 if success else 0} 个",
                        f"保存位置: {os.path.basename(base_dir)}"
                    ]

                    # 记录处理总结
                    self.detailed_logger.log_process_summary(product_name, sku, success, status)

                    return "\n".join(result)
                else:
                    result_msg = f"SKU {sku} 未找到{'任何匹配' if total_results == 0 else '符合条件的'}图片"
                    self.detailed_logger.log_process_summary(product_name, sku, False, result_msg)
                    return result_msg

            total_files = len(valid_files)

            # 下载API商品图片到内存，用于显示对比
            api_image_data = None
            if thumb_url:
                self.log(f"🔍 找到商品API图片: {thumb_url}", 'info')
                # 下载API图片到内存
                try:
                    # 设置请求头
                    api_headers = {
                        'User-Agent': self.headers['User-Agent'],
                        'Referer': self.api_referer,
                        'Accept': self.headers['Accept']
                    }

                    # 下载图片
                    response = requests.get(thumb_url, headers=api_headers, timeout=15)
                    response.raise_for_status()

                    # 从响应内容创建图像
                    api_image_data = Image.open(io.BytesIO(response.content))
                    self.log(f"✅ API商品图下载成功", 'success')

                except Exception as e:
                    self.log(f"❌ API商品图下载失败: {str(e)}", 'error')
                    api_image_data = None

            # 无论找到几张图片，都显示选择对话框
            self.log(f"📊 SKU {sku} 找到 {total_files} 张匹配图片，打开预览窗口", 'info')

            # 创建并显示图片选择对话框，传入API图片数据
            selection_dialog = ImageSelectionDialog(self.root, sku, valid_files, None, api_image_data)
            selected_idx = selection_dialog.wait_for_selection()

            # 记录用户选择
            selection_type = "API商品图" if selected_idx == -1 else "本地文件"
            self.detailed_logger.log_user_selection(selected_idx, total_files, selection_type)
            
            if selected_idx is not None:
                # 判断是否选择API图片
                if selected_idx == -1 and api_image_data:
                    # 用户选择了API图片
                    self.log(f"用户选择了API商品图", 'info')
                    local_path = os.path.join(base_dir, f"{sku}.jpg")

                    # 记录下载尝试
                    self.detailed_logger.log_download_attempt("内存中的API图片", local_path, "API商品图")

                    # 直接保存API图片数据到目标路径
                    try:
                        api_image_data.save(local_path)
                        success = True
                        self.log(f"✅ API商品图保存成功: {local_path}", 'success')
                    except Exception as e:
                        self.log(f"❌ API商品图保存失败: {str(e)}", 'error')
                        success = False

                    # 记录下载结果
                    self.detailed_logger.log_download_result(success, local_path)

                    status = "使用API图片"

                    result = [
                        f"SKU: {sku}",
                        f"商品: {product_name}",
                        f"用户选择: API商品图",
                        f"状态: {status}",
                        f"成功下载: {1 if success else 0} 个",
                        f"保存位置: {os.path.basename(base_dir)}"
                    ]

                    # 记录处理总结
                    self.detailed_logger.log_process_summary(product_name, sku, success, status)

                    return "\n".join(result)
                else:
                    # 用户选择了本地图片
                    item, file_path = valid_files[selected_idx]

                    # 下载用户选择的本地图片
                    original_ext = os.path.splitext(item['name'])[1].lower()
                    new_name = f"{sku}{original_ext}"
                    local_path = os.path.join(base_dir, new_name)
                    image_url = f"{self.everything_image_url}/{quote(file_path)}"

                    # 记录下载尝试
                    self.detailed_logger.log_download_attempt(image_url, local_path, "本地文件")

                    success = self.download_image(image_url, local_path)

                    # 记录下载结果
                    self.detailed_logger.log_download_result(success, local_path)

                    status = f"从本地文件下载图片"

                    result = [
                        f"SKU: {sku}",
                        f"商品: {product_name}",
                        f"找到图片: {total_files} 个",
                        f"用户选择: 第 {selected_idx+1} 张本地图片",
                        f"状态: {status}",
                        f"成功下载: {1 if success else 0} 个",
                        f"保存位置: {os.path.basename(base_dir)}"
                    ]

                    # 记录处理总结
                    self.detailed_logger.log_process_summary(product_name, sku, success, status)

                    return "\n".join(result)
            else:
                # 用户取消了选择
                self.log(f"⚠️ 用户取消了SKU {sku} 的图片选择", 'warning')
                self.detailed_logger.log_process_summary(product_name, sku, False, "用户取消了图片选择")
                return f"SKU {sku}: 用户取消了图片选择"

        except Exception as e:
            self.detailed_logger.log_process_summary(product_name, sku, False, f"处理出错: {str(e)}")
            return f"处理SKU {sku} 过程出错: {str(e)}"
            
    def download_missing_sku(self):
        """下载缺失的SKU图片"""
        try:
            # 获取当前配置
            cookie = self.cookie_var.get().strip()
            search_path = self.search_path_var.get().strip()
            path_filter = self.path_filter_var.get().strip()
            enable_target_suffix = self.enable_target_suffix_var.get()
            ignore_filename_chars = self.ignore_filename_chars_var.get()
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            # 获取输入框中的SKU列表
            skus = []
            for line in self.product_data_area.get("1.0", tk.END).strip().split('\n'):
                sku = line.strip()
                if sku:
                    skus.append(sku)
            
            if not skus:
                messagebox.showwarning("警告", "没有要下载的SKU")
                return
                
            # 更新下载器配置
            ImageDownloader.enable_target_suffix = enable_target_suffix
            ImageDownloader.ignore_filename_chars = ignore_filename_chars
            
            # 更新忽略字符数配置
            try:
                prefix_count = int(self.ignore_prefix_count_var.get().strip())
                if prefix_count >= 0:
                    ImageDownloader.ignore_prefix_count = prefix_count
            except ValueError:
                pass
                
            try:
                suffix_count = int(self.ignore_suffix_count_var.get().strip())
                if suffix_count >= 0:
                    ImageDownloader.ignore_suffix_count = suffix_count
            except ValueError:
                pass
                
            if path_filter:
                if path_filter.startswith('\\'):
                    ImageDownloader.target_path_suffix = path_filter
                else:
                    ImageDownloader.target_path_suffix = '\\' + path_filter
            
            # 创建时间戳和输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"缺失SKU下载_{timestamp}_共{len(skus)}个"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name
            
            # 创建请求会话
            session = requests.Session()
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 设置正确的请求地址和参数
            api_url = self.api_sku_search_url
            
            self.log("\n" + "═" * 60 + "\n开始下载缺失SKU的图片...", 'info')
            self.update_status("正在处理SKU下载...")
            
            # 输出配置信息
            self.log(f"📝 当前配置: 搜索路径={search_path}, 目标路径={ImageDownloader.target_path_suffix}, 启用目标路径后缀={enable_target_suffix}", 'info')
            if ignore_filename_chars:
                self.log(f"📝 忽略文件名前{ImageDownloader.ignore_prefix_count}个字符和后{ImageDownloader.ignore_suffix_count}个字符", 'info')
                self.log(f"📝 已启用模糊匹配模式，将更宽松地匹配文件名", 'info')
            
            # 准备下载目录
            base_dir = os.path.join(self.current_output_dir, "SKU图片下载")
            ImageDownloader.prepare_directory(base_dir)
            
            # 保存SKU列表
            sku_list_path = os.path.join(folder_name, "SKU列表.txt")
            with open(sku_list_path, 'w', encoding='utf-8') as f:
                for sku in skus:
                    f.write(f"{sku}\n")
            
            # 处理每个SKU
            success_count = 0
            for idx, sku in enumerate(skus, 1):
                try:
                    self.update_status(f"处理中: SKU {sku} ({idx}/{len(skus)})")
                    self.log(f"\n[{idx}/{len(skus)}] 🔍 正在查询SKU: {sku}", 'info')
                    
                    # 设置请求参数 - 使用正确的参数格式
                    payload = {
                        'sortName': '2',
                        'pageNo': '1',
                        'pageSize': '50',
                        'searchType': '2',
                        'searchValue': sku,
                        'productSearchType': '1',
                        'shopId': '-1',
                        'dxmState': 'online',
                        'site': '0',
                        'fullCid': '',
                        'sortValue': '2',
                        'productType': '',
                        'productStatus': 'ONLINE'
                    }
                    
                    # 记录API请求
                    self.detailed_logger.log_api_request(api_url, payload)

                    # 发送请求获取商品信息
                    response = session.post(api_url, headers=headers, data=payload, timeout=30)
                    response.raise_for_status()

                    # 解析JSON响应
                    json_data = response.json()

                    # 记录API响应状态
                    self.detailed_logger.log_api_request(api_url, payload, f"HTTP {response.status_code}")

                    # 检查是否成功
                    if json_data.get('code') != 0 or 'data' not in json_data:
                        error_msg = json_data.get('msg', '未知错误')
                        self.log(f"⚠️ 查询SKU {sku} 返回错误: {error_msg}", 'warning')
                        self.detailed_logger.log_api_response(json_data)
                        continue

                    # 检查是否有商品数据
                    product_list = json_data.get('data', {}).get('page', {}).get('list', [])
                    if not product_list:
                        self.log(f"⚠️ 未找到SKU {sku} 的商品信息", 'warning')
                        self.detailed_logger.log_api_response(json_data)
                        continue

                    # 获取第一个商品信息
                    product_info = product_list[0]
                    product_name = product_info.get('productName', '')

                    if not product_name:
                        self.log(f"⚠️ 未找到SKU {sku} 的商品名称", 'warning')
                        self.detailed_logger.log_api_response(json_data)
                        continue

                    self.log(f"📝 找到商品: {product_name}", 'info')

                    # 获取variations信息和thumbUrl
                    thumb_url = None
                    variations = product_info.get('variations', [])
                    if variations:
                        thumb_url = variations[0].get('thumbUrl')
                        if thumb_url:
                            self.log(f"🖼️ 获取到商品图片URL: {thumb_url}", 'info')
                        else:
                            self.log(f"⚠️ 未找到SKU {sku} 的thumbUrl", 'warning')
                    else:
                        self.log(f"⚠️ 未找到SKU {sku} 的variations信息", 'warning')

                    # 记录API响应
                    self.detailed_logger.log_api_response(json_data, product_name, thumb_url)
                    
                    # 使用商品名称搜索图片，并传入thumbUrl用于对比
                    result = self.process_sku_product(sku, product_name, base_dir, search_path, thumb_url)
                    if "成功下载" in result:
                        success_count += 1
                    self.log(result, 'info')
                    
                except Exception as e:
                    self.log(f"❌ 处理SKU {sku} 时出错: {str(e)}", 'error')
            
            # 汇总结果
            summary = f"\n═" * 30 + f"\n总计处理: {len(skus)} 个SKU, 成功下载: {success_count} 个\n文件保存在: {os.path.abspath(base_dir)}"
            self.log(summary, 'success')
            self.update_status("SKU下载完成")
            messagebox.showinfo("完成", f"缺失SKU图片下载完成！\n成功: {success_count}/{len(skus)}")
            
        except Exception as e:
            self.log(f"SKU下载过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"SKU下载过程出错: {str(e)}")

    def search_sku_name(self):
        """根据SKU搜索商品名称"""
        try:
            # 关闭之前的预览窗口
            self.close_preview_window()
            
            # 获取SKU输入
            sku = self.sku_search_entry.get().strip()
            if not sku:
                messagebox.showwarning("警告", "请输入SKU")
                return
                
            # 获取当前配置
            cookie = self.cookie_var.get().strip()
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            self.log(f"\n" + "═" * 60 + f"\n🔍 开始搜索SKU: {sku} 的商品名称...", 'info')
            self.update_status(f"正在搜索SKU: {sku}")
            
            # 设置请求头
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 设置正确的请求地址和参数
            api_url = self.api_sku_search_url
            
            # 设置请求参数
            payload = {
                'sortName': '2',
                'pageNo': '1',
                'pageSize': '50',
                'searchType': '2',
                'searchValue': sku,
                'productSearchType': '1',
                'shopId': '-1',
                'dxmState': 'online',
                'site': '0',
                'fullCid': '',
                'sortValue': '2',
                'productType': '',
                'productStatus': 'ONLINE'
            }
            
            # 发送请求获取商品信息
            response = requests.post(api_url, headers=headers, data=payload, timeout=30)
            response.raise_for_status()
            
            # 解析JSON响应
            json_data = response.json()
            
            # 检查是否成功
            if json_data.get('code') != 0 or 'data' not in json_data:
                error_msg = json_data.get('msg', '未知错误')
                self.log(f"❌ 查询失败: {error_msg}", 'error')
                messagebox.showerror("错误", f"查询失败: {error_msg}")
                return
                
            # 检查是否有商品数据
            product_list = json_data.get('data', {}).get('page', {}).get('list', [])
            if not product_list:
                self.log(f"⚠️ 未找到SKU {sku} 的商品信息", 'warning')
                messagebox.showinfo("提示", f"未找到SKU {sku} 的商品信息")
                return
                
            # 获取第一个商品信息
            product_info = product_list[0]
            product_name = product_info.get('productName', '')
            
            if not product_name:
                self.log(f"⚠️ 未找到SKU {sku} 的商品名称", 'warning')
                messagebox.showinfo("提示", f"未找到SKU {sku} 的商品名称")
                return
                
            # 显示结果
            self.log(f"✅ 找到商品: {product_name}", 'success')
            
            # 将商品名称复制到剪贴板
            pyperclip.copy(product_name)
            self.log(f"📋 已将商品名称复制到剪贴板", 'success')
            
            # 将SKU和商品名称添加到输入框
            current_text = self.product_data_area.get("1.0", tk.END).strip()
            if current_text:
                # 如果已有内容，添加到新行
                new_text = f"{current_text}\n{product_name}----{sku}"
            else:
                # 如果没有内容，直接添加
                new_text = f"{product_name}----{sku}"
                
            self.product_data_area.delete("1.0", tk.END)
            self.product_data_area.insert("1.0", new_text)
            
            # 获取商品图片URL
            thumb_url = None
            variations = product_info.get('variations', [])
            if variations:
                thumb_url = variations[0].get('thumbUrl')
                
            # 显示商品图片预览
            if thumb_url:
                self.show_product_preview(sku, product_name, thumb_url)
            else:
                self.log(f"⚠️ 未找到SKU {sku} 的商品图片", 'warning')
            
            # 更新状态
            self.update_status(f"已找到SKU {sku} 的商品名称")
            
        except requests.RequestException as e:
            self.log(f"❌ 请求失败: {str(e)}", 'error')
            messagebox.showerror("错误", f"API请求失败: {str(e)}")
        except Exception as e:
            self.log(f"❌ 搜索过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"搜索过程出错: {str(e)}")

    def close_preview_window(self):
        """关闭预览窗口"""
        if self.preview_window and self.preview_window.winfo_exists():
            self.preview_window.destroy()
            self.preview_window = None
            self.log("🔄 关闭之前的预览窗口", 'info')

    def show_product_preview(self, sku, product_name, thumb_url):
        """显示商品图片预览窗口"""
        try:
            # 关闭之前的预览窗口
            self.close_preview_window()
            
            # 创建预览窗口
            self.preview_window = tk.Toplevel(self.root)
            self.preview_window.title(f"商品预览 - {sku}")
            self.preview_window.geometry("400x450")
            self.preview_window.transient(self.root)  # 设置为主窗口的临时窗口
            
            # 设置窗口关闭事件
            self.preview_window.protocol("WM_DELETE_WINDOW", lambda: self.close_preview_window())
            
            # 设置窗口居中
            self.preview_window.update_idletasks()
            width = self.preview_window.winfo_width()
            height = self.preview_window.winfo_height()
            x = (self.preview_window.winfo_screenwidth() // 2) - (width // 2)
            y = (self.preview_window.winfo_screenheight() // 2) - (height // 2)
            self.preview_window.geometry(f'{width}x{height}+{x}+{y}')
            
            # 创建主框架
            main_frame = tk.Frame(
                self.preview_window,
                bg=StyleConfig.COLOR_SCHEME['primary_bg'],
                padx=15,
                pady=15
            )
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 商品信息区域
            info_frame = tk.Frame(
                main_frame,
                bg=StyleConfig.COLOR_SCHEME['primary_bg']
            )
            info_frame.pack(fill=tk.X, pady=(0, 10))
            
            # SKU标签
            sku_label = tk.Label(
                info_frame,
                text=f"SKU: {sku}",
                font=StyleConfig.FONT_SETTINGS['normal'],
                fg=StyleConfig.COLOR_SCHEME['primary_text'],
                bg=StyleConfig.COLOR_SCHEME['primary_bg'],
                anchor='w'
            )
            sku_label.pack(fill=tk.X)
            
            # 商品名称标签
            name_label = tk.Label(
                info_frame,
                text=f"商品名称: {product_name}",
                font=StyleConfig.FONT_SETTINGS['normal'],
                fg=StyleConfig.COLOR_SCHEME['primary_text'],
                bg=StyleConfig.COLOR_SCHEME['primary_bg'],
                anchor='w',
                wraplength=370  # 设置文本换行宽度
            )
            name_label.pack(fill=tk.X)
            
            # 图片区域
            image_frame = tk.Frame(
                main_frame,
                bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
                bd=1,
                relief=tk.GROOVE
            )
            image_frame.pack(fill=tk.BOTH, expand=True)
            
            # 图片加载中标签
            loading_label = tk.Label(
                image_frame,
                text="图片加载中...",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['secondary_bg']
            )
            loading_label.pack(fill=tk.BOTH, expand=True)
            
            # 按钮区域
            button_frame = tk.Frame(
                main_frame,
                bg=StyleConfig.COLOR_SCHEME['primary_bg']
            )
            button_frame.pack(fill=tk.X, pady=(10, 0))
            
            # 复制名称按钮
            copy_name_btn = tk.Button(
                button_frame,
                text="复制商品名称",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['accent_blue'],
                fg='white',
                relief=tk.FLAT,
                padx=10,
                command=lambda: pyperclip.copy(product_name)
            )
            copy_name_btn.pack(side=tk.LEFT, padx=(0, 5))
            
            # 复制SKU按钮
            copy_sku_btn = tk.Button(
                button_frame,
                text="复制SKU",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['accent_green'],
                fg='white',
                relief=tk.FLAT,
                padx=10,
                command=lambda: pyperclip.copy(sku)
            )
            copy_sku_btn.pack(side=tk.LEFT, padx=5)
            
            # 关闭按钮
            close_btn = tk.Button(
                button_frame,
                text="关闭",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['accent_gray'],
                fg='white',
                relief=tk.FLAT,
                padx=10,
                command=self.close_preview_window
            )
            close_btn.pack(side=tk.RIGHT)
            
            # 在后台线程中下载和显示图片
            threading.Thread(
                target=self._load_preview_image,
                args=(thumb_url, image_frame, loading_label),
                daemon=True
            ).start()
            
        except Exception as e:
            self.log(f"❌ 显示预览窗口出错: {str(e)}", 'error')
    
    def _load_preview_image(self, thumb_url, image_frame, loading_label):
        """在后台线程中加载预览图片"""
        try:
            # 设置请求头
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Referer': self.api_referer,
                'Accept': self.headers['Accept']
            }
            
            # 尝试下载图片，最多重试3次
            for attempt in range(3):
                try:
                    if attempt > 0:
                        time.sleep(2)  # 重试前等待2秒
                        
                    response = requests.get(thumb_url, headers=headers, timeout=15)
                    response.raise_for_status()
                    
                    # 从响应内容创建图像
                    img = Image.open(io.BytesIO(response.content))
                    
                    # 调整图像大小，保持纵横比
                    img.thumbnail((350, 350), Image.LANCZOS)
                    
                    # 创建Tkinter可用的图像对象
                    photo = ImageTk.PhotoImage(img)
                    
                    # 在主线程中更新UI
                    self.root.after(0, lambda: self._update_preview_image(image_frame, loading_label, photo))
                    break
                    
                except Exception as e:
                    if attempt == 2:  # 最后一次尝试失败
                        self.root.after(0, lambda: self._show_preview_error(loading_label, str(e)))
            
        except Exception as e:
            self.root.after(0, lambda: self._show_preview_error(loading_label, str(e)))
    
    def _update_preview_image(self, image_frame, loading_label, photo):
        """在主线程中更新预览图片"""
        try:
            # 移除加载中标签
            loading_label.pack_forget()
            
            # 创建图片标签
            image_label = tk.Label(
                image_frame,
                image=photo,
                bg=StyleConfig.COLOR_SCHEME['secondary_bg']
            )
            image_label.image = photo  # 保持引用
            image_label.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
        except Exception as e:
            self._show_preview_error(loading_label, str(e))
    
    def _show_preview_error(self, loading_label, error_msg):
        """显示预览图片加载错误"""
        loading_label.config(
            text=f"加载图片失败: {error_msg}",
            fg=StyleConfig.COLOR_SCHEME['accent_orange']
        )


if __name__ == "__main__":
    root = tk.Tk()
    app = ModernProductApp(root)
    root.mainloop() 