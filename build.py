import PyInstaller.__main__
import os
import sys
import time

print("开始打包过程...")
print(f"当前Python版本: {sys.version}")

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))
print(f"当前工作目录: {current_dir}")

# 定义主程序路径
main_script = os.path.join(current_dir, '半托找图.py')
print(f"主程序路径: {main_script}")

# 定义打包参数
params = [
    main_script,  # 主程序
    '--name=半托找图',  # 生成的exe名称
    '--onefile',  # 打包成单个exe文件
    '--windowed',  # 不显示控制台窗口
    '--noconfirm',  # 不询问确认
    '--clean',  # 清理临时文件
    '--debug=all',  # 添加调试信息
    '--hidden-import=PIL._tkinter_finder',  # 添加隐藏导入
]

print(f"打包参数: {params}")
print("开始执行PyInstaller打包...")
start_time = time.time()

try:
    # 执行打包
    PyInstaller.__main__.run(params)
    
    # 检查是否成功生成文件
    dist_dir = os.path.join(current_dir, 'dist')
    exe_path = os.path.join(dist_dir, '半托找图.exe')
    
    if os.path.exists(exe_path):
        print(f"\n打包成功! 用时: {time.time() - start_time:.2f}秒")
        print(f"生成的可执行文件路径: {exe_path}")
        print(f"文件大小: {os.path.getsize(exe_path) / (1024*1024):.2f} MB")
    else:
        print(f"\n警告: 未找到生成的可执行文件: {exe_path}")
        print("请检查日志获取详细错误信息")
except Exception as e:
    print(f"\n打包过程中出错: {str(e)}")
    print("请检查以上日志获取详细错误信息") 